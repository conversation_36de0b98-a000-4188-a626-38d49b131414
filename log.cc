#ifdef _WIN32
#include <Windows.h>
#endif
#include <sys/time.h>
#include <time.h>
#include <string.h>
#include <stdarg.h>  // 添加这一行，解决 va_start 和 va_end 未声明的问题
#include <iostream>
#include <sstream>
#include <string>
#include <vector>
#include <map>
#include <unordered_map>
#include <algorithm>
#include <chrono>
#include <thread>
#include <cstdint>
#include <fstream>

#ifdef _WIN32
#include <intrin.h>
#else
#include <x86intrin.h>
#include <unistd.h> // 添加unistd.h头文件，用于readlink函数
#endif

#include "md_api.h"
#include "strategy_api.h"
#include "trade_api.h"
#include "include/nlohmann/json.hpp"

using namespace std;
using json = nlohmann::json;

const char *kStockAccount = "567212";
const char *kSHProductId = "SH.601211";

int orderCount = 0;
CancelDetail *cancelList = NULL;
int cancelCount = 0;
static char *symbol_for_subscribe = NULL;

// 全局日志缓冲区，用于高性能日志打印，避免每次分配内存
static char g_log_buffer[512];

// todo 1. 2阶段下单。
// todo 2. 测试单
// todo 3. 撤单能力
// 下单请求 - 声明为全局变量
static OrderReq req; // 零初始化所有成员

// 在函数中设置其他值
void init_order_req() {
    memset(&req, 0x00, sizeof(OrderReq));

    req.order_type = OrderType_LMT;
    req.side = OrderSide::OrderSide_Bid;
    req.hedge_flag = HedgeFlag::HedgeFlag_Placeholder;
}

// 高性能股票代码管理系统
constexpr size_t MAX_SYMBOLS = 64; // 最多支持64只股票
constexpr size_t SYMBOL_SIZE = 12; // 股票代码固定12字节
struct timeval tv;
long long start;
string test_order = "0";
// 紧凑数组结构 - 只存储活跃且需要监控的股票索引
struct CompactArray {
    int indices[64]; // 存储原始数组的索引
    int count; // 当前紧凑数组中的元素数量

    CompactArray() : count(0) {
        memset(indices, -1, sizeof(indices));
    }

    // 清空数组
    void clear() {
        count = 0;
        memset(indices, -1, sizeof(indices));
    }

    // 添加索引
    void add(int index) {
        if (count < 64 && index >= 0) {
            indices[count++] = index;
        }
    }

    // 移除指定索引
    void remove(int index) {
        for (int i = 0; i < count; ++i) {
            if (indices[i] == index) {
                // 将最后一个元素移到要删除的位置
                indices[i] = indices[count - 1];
                indices[count - 1] = -1;
                --count;
                break;
            }
        }
    }

    // 检查是否包含某个索引
    bool contains(int index) const {
        for (int i = 0; i < count; ++i) {
            if (indices[i] == index) {
                return true;
            }
        }
        return false;
    }
};

// 全局紧凑数组 - 只包含活跃且需要监控的股票
static CompactArray g_active_watching_symbols;

// 股票代码存储结构
struct SymbolEntry {
    char symbol[SYMBOL_SIZE];
    bool is_active;
    uint32_t hash; // 预计算的哈希值

    SymbolEntry() : is_active(false), hash(0) {
        memset(symbol, 0, SYMBOL_SIZE);
    }
};

// 全局股票代码表
static SymbolEntry g_symbol_table[MAX_SYMBOLS];
static size_t g_symbol_count = 0;


// 极速股票代码比较（3ns延时）
inline bool is_symbol_match(const char symbol1[SYMBOL_SIZE], const char symbol2[SYMBOL_SIZE]) {
    // 使用64位+32位比较，覆盖全部12字节
    const uint64_t *sym1_64 = reinterpret_cast<const uint64_t *>(symbol1);
    const uint64_t *sym2_64 = reinterpret_cast<const uint64_t *>(symbol2);

    // 比较前8字节
    if (sym1_64[0] != sym2_64[0]) return false;

    // 比较后4字节
    const uint32_t *sym1_32 = reinterpret_cast<const uint32_t *>(symbol1 + 8);
    const uint32_t *sym2_32 = reinterpret_cast<const uint32_t *>(symbol2 + 8);
    return sym1_32[0] == sym2_32[0];
}


// 安全地将string转换为char[12]格式
inline void string_to_symbol(const string &str, char symbol[12]) {
    memset(symbol, 0, 12); // 先清零
    size_t len = str.length();
    if (len > 11) len = 11; // 最多复制11字节，留1字节给'\0'
    memcpy(symbol, str.c_str(), len);
}

// 自动添加市场前缀的转换函数
inline void code_to_symbol(const string &code, char symbol[12]) {
    memset(symbol, 0, 12);

    if (code.length() == 6) {
        // 纯数字代码，自动添加前缀
        if (code[0] == '6') {
            // 6开头 -> SH.
            memcpy(symbol, "SH.", 3);
            memcpy(symbol + 3, code.c_str(), 6);
        } else if (code[0] == '0' || code[0] == '3') {
            // 0或3开头 -> SZ.
            memcpy(symbol, "SZ.", 3);
            memcpy(symbol + 3, code.c_str(), 6);
        } else {
            // 其他情况，直接复制
            size_t len = code.length();
            if (len > 11) len = 11;
            memcpy(symbol, code.c_str(), len);
        }
    } else {
        // 已有前缀或其他格式，直接复制
        size_t len = code.length();
        if (len > 11) len = 11;
        memcpy(symbol, code.c_str(), len);
    }
}

#ifdef _WIN32
#define strncpy strncpy_s
#endif

typedef struct t_Orderid {
    int ntime; //下单行情时间
    int res; //保留
    char sorderid[32]; //委托号
} T_ORDERID;

vector<T_ORDERID> g_orderids;

// 预分配固定大小数组，避免动态内存分配
constexpr size_t MAX_ORDER_PAIRS = 100; // 最多支持100个订单关联
static size_t g_order_pair_count = 0;

// 全局map，保存每个股票代码对应的最新tick数据
map<string, SecurityTickData> g_latest_ticks;
// 事先分配 - 涨停下单执行情况

string log_config = "0";
double limit_up_price_allo = 0.0;

// 市场类型枚举
enum MarketType {
    SH = 0, // 上海市场
    SZ = 1 // 深圳市场
};

// 封单检测订阅信息（支持SH/SZ双市场）- 缓存行对齐优化
struct alignas(64) LimitUpSubscription {
    char symbol[12]; // 股票代码，使用char[12]避免动态分配
    MarketType market_type; // 市场类型
    int64_t threshold_volume; // 封单阈值（股数，SH直接比较；SZ用于计算金额）
    bool is_active; // 是否激活
    bool is_ordered; // 是否已经下单
    uint64_t limit_up_price; // 涨停价（用于快速比较）
    uint64_t limit_down_price; // 跌停价
    uint64_t threshold_price; // 7%涨幅价格阈值
    uint64_t pre_close_price; // 前收盘价
    // 填充至64字节
    uint8_t padding[9]; // 18字节填充（调整了填充大小）

    LimitUpSubscription() : market_type(SH), threshold_volume(0), is_active(false), is_ordered(false),
                            limit_up_price(0), limit_down_price(0), threshold_price(0), pre_close_price(0) {
        memset(symbol, 0, 12);
        memset(padding, 0, sizeof(padding));
    }
};


// SH大买单跟踪结构（简化版：只跟踪最新的一个）
struct SHLargeBuyOrder {
    int64_t bid_no;           // 买方订单号
    int64_t total_qty;        // 累计成交量
    int64_t last_update_time; // 最后更新时间

    SHLargeBuyOrder() : bid_no(0), total_qty(0), last_update_time(0) {}
};

// SZ市场专用的实时数据 - 缓存行对齐优化
struct alignas(64) SZRealTimeData {
    // 数据成员
    int64_t base_ask_volume; // 基准总卖量（来自tick的total_ask_vol）
    int64_t consumed_buy_volume; // 3秒内消耗的涨停价买单量
    int64_t initial_seal_volume; // 初始封单量（ask1_vol）
    int64_t current_seal_volume; // 当前封单量
    int64_t order_time; // 如果下单了，下单时间，否则0
    int64_t last_seal_volume;
    // 新增：成交金额风控相关字段
    int64_t order_start_turnover; // 下单时的总成交金额，用于计算期间成交量
    int64_t last_cancel_time; // 上次撤单时间，用于慢排版10秒时间窗口判断
    // 新增：提前扫板成交额限制字段
    int64_t current_turnover; // 当前总成交额，用于提前扫板1亿成交额判断
    // 新增：记录涨停时间，用于5分钟监控时间限制
    int64_t limitup_time; // 最近一次涨停时间，用于5分钟监控时间限制

    // 新增：跨tick封单跟踪字段（10秒内20%撤单能力）
    int64_t seal_volume_history[4]; // 循环存储最近4个tick的封单量
    int64_t tick_time_history[4]; // 循环存储最近4个tick的时间
    int tick_index; // 当前tick在数组中的索引（0-3循环）
    int tick_count; // 总tick计数，用于判断是否有足够历史数据
    
    char order_id[32]; // 订单号，字符串格式
    bool risk_control_triggered; // 是否已触发风控撤单
    bool slow_mode_triggered; // 是否已触发慢排版模式
    bool is_limit_up; // 是否涨停状态
    bool is_ready; 
    bool has_sweep_order; // 标识是否有扫板情况（涨幅小于9点）
    // SH大买单跟踪（只跟踪最新的一个）
    SHLargeBuyOrder sh_current_large_order;
    // 填充至64字节

    uint16_t num_order0; // 0号订单数量
    uint16_t num_order10; // 1号订单数量
    uint16_t num_order20; // 2号订单数量
    uint16_t num_order30; // 3号订单数量
    uint16_t num_order40; // 4号订单数量
    uint16_t num_order50; // 5号订单数量
    uint16_t num_order60; // 6号订单数量
    uint8_t padding2[18]; // 调整填充大小，减少8字节以适应新增的limitup_time

    // 计算剩余卖量
    int64_t GetRemainingAskVolume() const {
        return base_ask_volume - consumed_buy_volume;
    }

    // 更新SH大买单（简化版：只跟踪最新的一个）
    void UpdateSHLargeBuyOrder(int64_t bid_no, int64_t qty, int64_t update_time) {
        // 如果是同一个买方订单号，累加成交量
        if (sh_current_large_order.bid_no == bid_no) {
            sh_current_large_order.total_qty += qty;
            sh_current_large_order.last_update_time = update_time;
        } else {
            // 如果是新的买方订单号，切换到新的大买单
            sh_current_large_order.bid_no = bid_no;
            sh_current_large_order.total_qty = qty;
            sh_current_large_order.last_update_time = update_time;
        }
    }

    // 检查当前大买单是否满足条件
    bool CheckSHLargeBuyOrderCondition() const {
        // 检查当前大买单是否超过30万股（300000）
        if (sh_current_large_order.total_qty > 300000) {
            // 检查剩余卖量是否不足这个大买单的量
            int64_t remaining_ask_vol = GetRemainingAskVolume();
            if (remaining_ask_vol < sh_current_large_order.total_qty) {
                return true;
            }
        }
        return false;
    }
};

// 高性能订阅管理器 - 使用数组替代unordered_map
constexpr size_t MAX_SUBSCRIPTIONS = 200; // 最多支持32只股票
static LimitUpSubscription g_limitup_subscriptions[MAX_SUBSCRIPTIONS];
static SZRealTimeData g_sz_realtime_data[MAX_SUBSCRIPTIONS];
static int g_subscription_count = 0;

// 高性能股票代码比较函数
inline bool is_symbol_equal(const char symbol1[12], const char symbol2[12]) {
    // 使用64位+32位比较，覆盖全部12字节，延时约3ns
    const uint64_t *sym1_64 = reinterpret_cast<const uint64_t *>(symbol1);
    const uint64_t *sym2_64 = reinterpret_cast<const uint64_t *>(symbol2);

    // 比较前8字节
    if (sym1_64[0] != sym2_64[0]) return false;

    // 比较后4字节
    const uint32_t *sym1_32 = reinterpret_cast<const uint32_t *>(symbol1 + 8);
    const uint32_t *sym2_32 = reinterpret_cast<const uint32_t *>(symbol2 + 8);
    return sym1_32[0] == sym2_32[0];
}

// 查找订阅索引（热路径优化）- 使用紧凑数组加速
inline int find_origin_subscription_index(const char symbol[12]) {
    for (int i = 0; i < g_subscription_count; ++i) {
        if (g_limitup_subscriptions[i].is_active &&
            is_symbol_equal(g_limitup_subscriptions[i].symbol, symbol)) {
            return i;
        }
    }

    return -1; // 未找到
}

// 查找所有订阅索引（包括非激活的）- 用于检查重复订阅
inline int find_any_subscription_index(const char symbol[12]) {
    for (int i = 0; i < g_subscription_count; ++i) {
        if (is_symbol_equal(g_limitup_subscriptions[i].symbol, symbol)) {
            return i;
        }
    }

    return -1; // 未找到
}

// 查找订阅索引（热路径优化）- 使用紧凑数组加速
inline int find_subscription_index(const char symbol[12]) {
    // 先在紧凑数组中查找
    for (int i = 0; i < g_active_watching_symbols.count; ++i) {
        int idx = g_active_watching_symbols.indices[i];
        if (is_symbol_equal(g_limitup_subscriptions[idx].symbol, symbol)) {
            return idx;
        }
    }

    return -1; // 未找到
}


// 获取指定股票的最新tick数据
SecurityTickData *GetLatestTick(const string &symbol) {
    auto it = g_latest_ticks.find(symbol);
    if (it != g_latest_ticks.end()) {
        return &(it->second);
    }
    return nullptr; // 未找到该股票的tick数据
}

// 检查是否有指定股票的tick数据
bool HasTickData(const string &symbol) {
    return g_latest_ticks.find(symbol) != g_latest_ticks.end();
}

// 获取当前缓存的股票数量
size_t GetTickDataCount() {
    return g_latest_ticks.size();
}

// 识别市场类型 - 优化为char[12]版本
MarketType GetMarketType(const char symbol[12]) {
    // 高性能前缀比较，避免string构造
    if (symbol[0] == 'S' && symbol[1] == 'H' && symbol[2] == '.') {
        return SH; // 上海市场
    } else if (symbol[0] == 'S' && symbol[1] == 'Z' && symbol[2] == '.') {
        return SZ; // 深圳市场
    }
    return SH; // 默认上海市场
}

// 初始化实时数据的公共函数
inline void InitializeRealTimeData(SZRealTimeData &realtime_data) {
    memset(&realtime_data, 0, sizeof(SZRealTimeData));
    realtime_data.base_ask_volume = 9999999999;
    realtime_data.consumed_buy_volume = 0;
    // 初始化封单相关字段
    realtime_data.initial_seal_volume = 0;
    realtime_data.current_seal_volume = 0;
    realtime_data.order_time = 0;
    memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));
    realtime_data.is_limit_up = false;
    realtime_data.is_ready = false;
    // 初始化成交金额风控字段
    realtime_data.order_start_turnover = 0;
    realtime_data.risk_control_triggered = false;
    realtime_data.slow_mode_triggered = false;
    realtime_data.last_cancel_time = 0;
    // 初始化阈值为0时的提前下单判断字段
    realtime_data.has_sweep_order = false;
    // 初始化提前扫板成交额限制字段
    realtime_data.current_turnover = 0;
    // 初始化涨停时间
    realtime_data.limitup_time = 0;
    // 初始化跨tick封单跟踪字段
    memset(realtime_data.seal_volume_history, 0, sizeof(realtime_data.seal_volume_history));
    memset(realtime_data.tick_time_history, 0, sizeof(realtime_data.tick_time_history));
    realtime_data.tick_index = 0;
    realtime_data.tick_count = 0;
    // 初始化SH大买单跟踪
    memset(&realtime_data.sh_current_large_order, 0, sizeof(realtime_data.sh_current_large_order));
}


// 订阅涨停板封单检测（支持SH/SZ双市场）- 优化为char[12]版本
void SubscribeLimitUpDetection(const char symbol[12], int64_t threshold_volume) {
    // 检查是否已达到最大订阅数
    if (g_subscription_count >= MAX_SUBSCRIPTIONS) {
        return; // 订阅表满
    }

    // 检查是否已存在（包括非激活的订阅）
    int existing_index = find_any_subscription_index(symbol);
    if (existing_index >= 0) {
        // 重新激活现有订阅，更新阈值
        g_limitup_subscriptions[existing_index].is_active = true;
        g_limitup_subscriptions[existing_index].is_ordered = false;
        g_limitup_subscriptions[existing_index].threshold_volume = threshold_volume;

        // 重置对应的实时数据
        SZRealTimeData &realtime_data = g_sz_realtime_data[existing_index];
        InitializeRealTimeData(realtime_data);

        return;
    }

    // 识别市场类型
    MarketType market_type = GetMarketType(symbol);

    // 创建新订阅
    LimitUpSubscription &subscription = g_limitup_subscriptions[g_subscription_count];
    memcpy(subscription.symbol, symbol, 12);
    subscription.market_type = market_type;
    subscription.threshold_volume = threshold_volume;
    subscription.is_ordered = false;
    subscription.is_active = true;
    subscription.limit_up_price = 0; // 将在tick更新时设置

    // 初始化对应的实时数据
    SZRealTimeData &realtime_data = g_sz_realtime_data[g_subscription_count];
    InitializeRealTimeData(realtime_data);

    // 增加订阅计数
    g_subscription_count++;

    ostringstream log;
    log << "已订阅涨停板封单检测: " << symbol
            << ", 市场: " << (market_type == SH ? "SH(简化)" : "SZ(复杂)")
            << ", 封单阈值: " << threshold_volume << " 股";
    strategy_log(StrategyLogLevel_Info, log.str().data());
}

// 取消涨停板封单检测订阅 - 优化为char[12]版本
bool UnsubscribeLimitUpDetection(const char symbol[12]) {
    // 查找订阅索引
    int index = find_subscription_index(symbol);
    if (index < 0) {
        ostringstream log;
        log << "取消订阅失败: 未找到股票 " << symbol << " 的订阅";
        strategy_log(StrategyLogLevel_Info, log.str().data());
        return false;
    }

    // 标记为非激活状态（不删除，避免数组重排）
    g_limitup_subscriptions[index].is_active = false;
    g_limitup_subscriptions[index].is_ordered = false;

    // 从紧凑数组中移除
    g_active_watching_symbols.remove(index);

    // 清理对应的实时数据
    SZRealTimeData &realtime_data = g_sz_realtime_data[index];
    realtime_data.order_time = 0;
    memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));

    ostringstream log;
    log << "成功取消订阅: " << symbol;
    strategy_log(StrategyLogLevel_Info, log.str().data());

    return true;
}

// 检查是否已订阅指定股票 - 优化为char[12]版本
bool IsSubscribed(const char symbol[12]) {
    return find_subscription_index(symbol) >= 0;
}

// 检查跨tick封单减少20%撤单：基于10秒内封单量变化监控
bool CheckCrossTickSealVolumeCancel(const char symbol[12], SZRealTimeData &realtime_data, int64_t current_time, int64_t limit_up_price) {
    // 如果没有下单，不需要检查
    if (realtime_data.order_time == 0 || strlen(realtime_data.order_id) == 0) {
        return false;
    }

    // 如果还没有4个tick的历史数据，不检查
    if (realtime_data.tick_count < 4) {
        return false;
    }

    // 计算4个tick前的索引
    int four_ticks_ago_index = (realtime_data.tick_index + 1) % 4; // 最老的数据

    // 检查4个tick前的时间是否在10秒内
    if (current_time - realtime_data.tick_time_history[four_ticks_ago_index] > 13000) {
        return false;
    }

    // 获取当前封单量和4个tick前的封单量
    int64_t current_seal_volume = realtime_data.current_seal_volume;
    int64_t seal_volume_4_ticks_ago = realtime_data.seal_volume_history[four_ticks_ago_index];

    // 检查当前封单量是否比4个tick前减少了20%
    if (seal_volume_4_ticks_ago > 0 && current_seal_volume * 10 < seal_volume_4_ticks_ago * 8) {
        // 触发跨tick撤单
        CancelDetail *cancelList = NULL;
        int cancelCount = 0;
        int ret = td_cancel_order(kStockAccount, AccountType_Stock, realtime_data.order_id,
                                  &cancelList, &cancelCount);
        if (ret != 0) {
            ostringstream log;
            log << "跨tick撤单失败: " << symbol
                << ", 订单号: " << realtime_data.order_id
                << ", 错误码: " << ret
                << ", 当前封单: " << current_seal_volume
                << ", 4个tick前封单: " << seal_volume_4_ticks_ago
                << ", 错误信息: " << hft_strerror(ret);
            strategy_log(StrategyLogLevel_Error, log.str().data());
        } else {
            ostringstream log;
            log << "跨tick撤单成功: " << symbol
                << ", 订单号: " << realtime_data.order_id
                << ", 当前封单: " << current_seal_volume
                << ", 4个tick前封单: " << seal_volume_4_ticks_ago
                << ", 减少比例: " << (100 - current_seal_volume * 100 / seal_volume_4_ticks_ago) << "%";
            strategy_log(StrategyLogLevel_Info, log.str().data());

            // 撤单成功后，重置下单状态
            realtime_data.order_time = 0;
            memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));
            realtime_data.last_cancel_time = current_time;
        }
        return true;
    }

    return false;
}

// 检查成交金额风控：基于tick数据监控1分钟内成交金额超过2000万则撤单
bool CheckTurnoverRiskControl(const char symbol[12], SZRealTimeData &realtime_data, int64_t price, int64_t current_turnover) {
    // 如果没有下单，不需要检查
    if (realtime_data.order_time == 0 || strlen(realtime_data.order_id) == 0) {
        return false;
    }

    // 计算下单后的成交金额（当前总成交金额 - 下单时总成交金额）
    int64_t period_turnover = current_turnover - realtime_data.order_start_turnover;

    // 检查是否超过2000万（************，已扩大1万倍）
    if (period_turnover * price > ************) {
        // 触发风控撤单
        CancelDetail *cancelList = NULL;
        int cancelCount = 0;
        int ret = td_cancel_order(kStockAccount, AccountType_Stock, realtime_data.order_id,
                                  &cancelList, &cancelCount);

        if (ret != 0) {
            snprintf(g_log_buffer, sizeof(g_log_buffer),
                     "风控撤单失败: %s, 订单号: %s, 错误码: %d, 错误信息: %s",
                     symbol, realtime_data.order_id,
                     ret, hft_strerror(ret));
            strategy_log(StrategyLogLevel_Error, g_log_buffer);
            return false;
        } else {
            snprintf(g_log_buffer, sizeof(g_log_buffer),
                     "风控撤单成功: %s, 订单号: %s, 超过2000万阈值",
                     symbol, realtime_data.order_id);
            strategy_log(StrategyLogLevel_Info, g_log_buffer);

            // 标记风控已触发，清理订单信息
            realtime_data.order_time = 0;
            memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));
            return true;
        }
    }

    return false;
}

// 检查是否需要撤单（基于逐笔数据时间和封单量）
bool CheckAndCancelOrder(const char symbol[12], int64_t current_data_time, SZRealTimeData &realtime_data,
                         int64_t limit_up_price) {
    const char *order_id = realtime_data.order_id;

    // 根据模式决定封单阈值
    int64_t cancel_threshold;
    if (!realtime_data.slow_mode_triggered) {
        // 快速模式：封单量小于5000万就撤单
        cancel_threshold = ************; // 5000万 * 10000
    } else {
        // 慢排版模式：封单量小于6000万就撤单
        cancel_threshold = ************; // 6000万 * 10000
    }

    // 检查封单量是否达到阈值
    if (realtime_data.current_seal_volume * limit_up_price >= cancel_threshold) {
        return false; // 封单量足够大，不需要撤单
    }
    CancelDetail *cancelList = NULL;
    int cancelCount = 0;
    // 执行撤单
    int ret = td_cancel_order(kStockAccount, AccountType_Stock, order_id,
                              &cancelList, &cancelCount);
    if (ret != 0) {
        ostringstream log;
        log << "撤单失败: " << symbol
                << ", 订单号: " << order_id
                << ", 错误码: " << ret
                << ", 错误信息: " << hft_strerror(ret);
        strategy_log(StrategyLogLevel_Error, log.str().data());
        return false;
    } else {
        // log << "撤单: " << symbol
        // << ", 订单号: " << order_id
        // << ", 下单时逐笔时间: " << realtime_data.order_time
        // << ", 当前逐笔时间: " << current_data_time
        // << ", 时间差: " << current_data_time - realtime_data.order_time << " ms"
        // << ", 当前封单量: " << realtime_data.current_seal_volume;
        // strategy_log(StrategyLogLevel_Info, log.str().data());

        // 撤单成功，标记订单为非活跃
        realtime_data.order_time = 0;
        memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));
        return true;
    }
}


// 涨停板下单函数 - 按金额下单，优化为char[12]版本
int OrderLimitUp_test(const char symbol[12], double amount, int64_t down_300, int64_t data_time) {
    // 根据金额和涨停价计算股数（100股的整数倍）
    memset(&req, 0x00, sizeof(OrderReq));
    memcpy(req.symbol, symbol, 12); // 直接复制char[12]
    req.order_type = OrderType_LMT; // 限价单
    req.side = OrderSide::OrderSide_Bid; // 买入
    req.volume = 100;
    req.price = down_300; // 使用跌停价+300
    req.hedge_flag = HedgeFlag::HedgeFlag_Placeholder;

    // 执行下单
    int rc = td_order(kStockAccount, AccountType_Stock, &req, 1);

    if (rc != 0) {
        // 使用全局预分配的缓冲区和sprintf替代ostringstream，提高性能
        snprintf(g_log_buffer, sizeof(g_log_buffer),
                 "测试单下单失败: %s, 错误码: %d, 错误信息: %s, req.ordertype: %d, req.side: %d, req.volume: %d, req.price: %lld, req.hedge_flag: %d, req.symbol: %s",
                 symbol, rc, hft_strerror_utf8(rc), req.order_type, req.side, req.volume, (long long) req.price,
                 req.hedge_flag, req.symbol);
        strategy_log(StrategyLogLevel_Error, g_log_buffer);
    }

    return rc;
}

// 涨停板下单函数 - 按金额下单，优化为char[12]版本
int OrderLimitUp(const char symbol[12], double amount, int64_t limit_up_price, int64_t data_time,
                 SZRealTimeData &realtime_data) {
    // 根据金额和涨停价计算股数（100股的整数倍）
    double price_yuan = limit_up_price / 10000.0; // 转换为元 todo 不用算
    int volume = (int) (amount / price_yuan / 100) * 100; // 向下取整到100股

    memcpy(req.symbol, symbol, 12); // 直接复制char[12]
    if (test_order == "1") {
        req.volume = 100;
        req.price = limit_up_price-500; // 使用涨停价
    }else {
        req.volume = volume;
        req.price = limit_up_price; // 使用涨停价
    }


    // 执行下单
    int rc = td_order(kStockAccount, AccountType_Stock, &req, 1);

    if (rc == 0) {
        // 保存订单号和股票代码关联，同时保存触发下单的逐笔数据时间
        realtime_data.order_time = data_time; // 保存触发下单的逐笔数据时间
        strncpy(realtime_data.order_id, req.order_id, sizeof(realtime_data.order_id) - 1); // 保存订单号
        // 重置成交金额风控字段
        realtime_data.order_start_turnover = 0; // 将在下一个tick中设置
    } else {
        // 使用全局预分配的缓冲区和sprintf替代ostringstream，提高性能
        snprintf(g_log_buffer, sizeof(g_log_buffer),
                 "涨停板下单失败: %s, 错误码: %d, 错误信息: %s, req.ordertype: %d, req.side: %d, req.volume: %d, req.price: %lld, req.hedge_flag: %d, req.symbol: %s",
                 symbol, rc, hft_strerror_utf8(rc), req.order_type, req.side, req.volume, (long long) req.price,
                 req.hedge_flag, req.symbol);
        strategy_log(StrategyLogLevel_Error, g_log_buffer);
    }

    return rc;
}


// SH市场封单检测（累积版）- 累积多笔涨停价委托，优化为char[12]版本
bool CheckAndExecuteSHLimitUpSeal(const char symbol[12], int64_t limit_up_volume, int64_t threshold_amount,
                                  int64_t time) {
    int index = find_subscription_index(symbol);
    if (index < 0) {
        return false;
    }

    return false;
}


// 返回当前日期时间，格式为YYYYMMDDhhmmss
static uint64_t datetime_now() {
    struct tm tmval;
    time_t time_now = time(NULL);
#ifdef _WIN32
    errno_t err = localtime_s(&tmval, &time_now);
    if (err != 0) {
        return err > 0 ? -err : err;
    }
#else
    errno = 0;
    struct tm* ret = localtime_r(&time_now, &tmval);
    if (ret == NULL) {
        return errno > 0 ? -errno : errno;
    }
#endif

    uint64_t ndate = (tmval.tm_year + 1900) * 10000 + (tmval.tm_mon + 1) * 100 +
                     tmval.tm_mday;
    uint64_t ntime = tmval.tm_hour * 10000 + tmval.tm_min * 100 + tmval.tm_sec;
    return ndate * 1000000 + ntime;
}

// 返回当前日期时间，格式为YYYY-MM-DD hh:mm:ss
static string str_datetime_now() {
    struct tm tmval;
    time_t time_now = time(NULL);
#ifdef _WIN32
    errno_t err = localtime_s(&tmval, &time_now);
    if (err != 0) {
        return "";
    }
#else
    errno = 0;
    struct tm* ret = localtime_r(&time_now, &tmval);
    if (ret == NULL) {
        return "";
    }
#endif

    char szTemp[32];
    snprintf(szTemp, sizeof(szTemp), "%04d-%02d-%02d %02d:%02d:%02d",
             tmval.tm_year + 1900, tmval.tm_mon + 1, tmval.tm_mday,
             tmval.tm_hour, tmval.tm_min, tmval.tm_sec);
    return szTemp;
}


// 证券Tick行情回调处理函数 - 优化为char[12]版本
void OnSecurityTick(SecurityTickData *res, void *user_data) {
    // 高性能查找订阅
    int sub_index = find_origin_subscription_index(res->symbol);
    if (sub_index >= 0 && g_limitup_subscriptions[sub_index].is_active) {
        if (res->time >= 145500000) {
            g_limitup_subscriptions[sub_index].is_active = false;

            // 从紧凑数组中移除
            g_active_watching_symbols.remove(sub_index);
            return;
        }
        // 更新涨停价和实时数据
        LimitUpSubscription &subscription = g_limitup_subscriptions[sub_index];
        SZRealTimeData &realtime_data = g_sz_realtime_data[sub_index];

        subscription.limit_up_price = res->high_limited;
        subscription.limit_down_price = res->low_limited;
        subscription.pre_close_price = res->pre_close;

        // 计算7%涨幅的价格阈值
        subscription.threshold_price = (uint64_t) (res->pre_close * 1.07);

        // 判断是否达到监听阈值（涨幅>7%）
        bool is_watching = (res->match >= subscription.threshold_price);

        realtime_data.consumed_buy_volume = 0;
        realtime_data.base_ask_volume = res->total_ask_vol;
        // 更新当前成交额，用于提前扫板1亿成交额判断
        realtime_data.current_turnover = res->turnover;

        // 判断是否涨停：match == 涨停价格 && bid_vol[0] == 0
        bool is_limit_up = (res->total_ask_vol == 0);

        // 处理涨停状态变化和5分钟监控逻辑
        if (is_limit_up) {
            // 股票处于涨停状态
            if (!realtime_data.is_limit_up) {
                // 从非涨停变为涨停，记录涨停时间
                realtime_data.limitup_time = res->time;
              
            }

            // 检查是否超过5分钟监控时间限制
            if (realtime_data.limitup_time > 0) {
                int64_t time_since_limitup = res->time - realtime_data.limitup_time;
                // 5分钟 = 300秒 = 300000毫秒
                if (time_since_limitup > 30000) {
                    // 超过5分钟，从监控列表中移除
                    g_active_watching_symbols.remove(sub_index);
                    
                    // 注意：不设置is_active=false，这样如果后续再次涨停可以继续监控
                }
            }

            realtime_data.initial_seal_volume = res->bid_vol[0];
            realtime_data.current_seal_volume = res->bid_vol[0];

            // 记录跨tick封单历史数据（每3秒记录一次，只在涨停时记录）
            realtime_data.seal_volume_history[realtime_data.tick_index] = res->bid_vol[0];
            realtime_data.tick_time_history[realtime_data.tick_index] = res->time;
            realtime_data.tick_index = (realtime_data.tick_index + 1) % 4; // 循环使用0-3
            realtime_data.tick_count++;

            // 检查跨tick封单减少20%撤单（10秒内）- 在SecurityTick中执行
            if (CheckCrossTickSealVolumeCancel(res->symbol, realtime_data, res->time, subscription.limit_up_price)) {
                subscription.is_ordered = false;
                // 跨tick撤单成功，继续监控但不再下单
            }
        } else if (realtime_data.is_limit_up) {
            // 从涨停变为非涨停，重置涨停时间，继续监控
            realtime_data.limitup_time = 0;
            
            snprintf(g_log_buffer, sizeof(g_log_buffer),
                     "股票脱离涨停: %s, 时间: %lld, 继续监控",
                     subscription.symbol, (long long)res->time);
            strategy_log(StrategyLogLevel_Info, g_log_buffer);
            
            // 确保股票在监控列表中
            if (!g_active_watching_symbols.contains(sub_index) && is_watching) {
                g_active_watching_symbols.add(sub_index);
            }
        }
        
        // 更新涨停状态
        realtime_data.is_limit_up = is_limit_up;
        realtime_data.is_ready = true;

        // 如果已经下单，检查成交金额风控
        // if (realtime_data.order_time > 0 && strlen(realtime_data.order_id) > 0) {
        //     // 如果是下单后的第一个tick，记录下单时的成交金额
        //     if (realtime_data.order_start_turnover == 0) {
        //         realtime_data.order_start_turnover = 1;
        //         return;
        //     }
        //     if (realtime_data.order_start_turnover == 1) {
        //         realtime_data.order_start_turnover = res->turnover;
        //         return;
        //     }
        //
        //     int64_t time_diff_us = res->time - realtime_data.order_time;
        //     if (time_diff_us > 60000) { // 超过60秒
        //         return; // 超过1分钟，不再检查
        //     }
        //
        //     bool risk_triggered = CheckTurnoverRiskControl(res->symbol, realtime_data,res->high_limited, res->turnover);
        //     if (risk_triggered) {
        //         // 风控撤单成功，停用该股票的监控
        //         subscription.is_active = false;
        //         subscription.is_ordered = false;
        //
        //         // 从紧凑数组中移除
        //         g_active_watching_symbols.remove(sub_index);
        //         return; // 直接返回，不再处理其他逻辑
        //     }
        // }

        // 更新紧凑数组 - 只保留活跃且需要监控的股票
        if (subscription.is_active && is_watching) {
            // 如果符合条件且不在紧凑数组中，则添加
            if (!g_active_watching_symbols.contains(sub_index) && !is_limit_up) {
                // 日志涨停了就不要加了
                g_active_watching_symbols.add(sub_index);
            }
        } else if (!is_watching) {
            // 如果不符合条件但在紧凑数组中，则移除
            g_active_watching_symbols.remove(sub_index);
        }
    }
}


// 证券K线行情回调处理函数
void OnSecurityKdata(SecurityKdata *res, void *user_data) {
    // 根据K线行情，使用这一分钟开盘价下单

    ostringstream oss;
    oss << "OnSecurityKdata: res: " << res->time << "  " << res->symbol << " " << res->high;

    strategy_log(StrategyLogLevel_Info, oss.str().data());

    return;
}

void UpdateOrderTimeCount(SZRealTimeData &realtime_data, int64_t time_diff) {
    if (time_diff == 0) {
        realtime_data.num_order0 += 1;
    } else if (time_diff == 10) {
        realtime_data.num_order10 += 1;
    } else if (time_diff == 20) {
        realtime_data.num_order20 += 1;
    } else if (time_diff == 30) {
        realtime_data.num_order30 += 1;
    } else if (time_diff == 40) {
        realtime_data.num_order40 += 1;
    } else if (time_diff == 50) {
        realtime_data.num_order50 += 1;
    } else if (time_diff == 60) {
        realtime_data.num_order60 += 1;
    }
}

inline int64_t cal_seal_vol(TickByTickData *tbt, LimitUpSubscription &subscription, SZRealTimeData &realtime_data);


// SH FCode订阅大单检测函数（基于逐笔成交数据）
inline bool check_sh_fcode_large_order(TickByTickData *tbt, LimitUpSubscription &subscription, SZRealTimeData &realtime_data) {
    if (tbt->type != '1') {
        return false;
    }

    TickByTickTrade &trade = tbt->data.trade;
    if (trade.price != subscription.limit_up_price) {
        return false;
    }

    // 只处理主动买入成交（trade_flag == 'B'）
    if (trade.trade_flag != 'B') {
        return false;
    }

    // 更新大买单跟踪
    realtime_data.UpdateSHLargeBuyOrder(trade.bid_no, trade.qty, tbt->data_time);

    // 检查是否有大买单满足条件
    if (realtime_data.CheckSHLargeBuyOrderCondition()) {
        return true; // 触发下单条件
    }

    return false;
}

inline bool check_sh_fcode_early_order(TickByTickData *tbt, LimitUpSubscription &subscription, SZRealTimeData &realtime_data) {
    // 条件1：base_ask_vol减少80%（通过consumed_buy_volume与base_ask_volume比较）
    // 条件2：有扫板的情况（涨幅小于9%）
    // 条件3：成交额已经大于1亿，充分换手
    // 三个条件必须同时满足（与关系）
    if (realtime_data.base_ask_volume > 0 &&
        realtime_data.consumed_buy_volume * 10 >= realtime_data.base_ask_volume * 9 &&
        realtime_data.current_turnover > 1000000000000) { // 1亿元 * 10000（扩大倍数）
        return true;
        }

    return false;
}

// SZ FCode订阅新的提前下单检测函数（阈值为0时使用）
inline bool check_sz_fcode_early_order(TickByTickData *tbt, LimitUpSubscription &subscription, SZRealTimeData &realtime_data) {
    // 条件1：base_ask_vol减少80%（通过consumed_buy_volume与base_ask_volume比较）
    // 条件2：有扫板的情况（涨幅小于9%）
    // 两个条件必须同时满足（与关系）
    if (realtime_data.base_ask_volume > 0 &&
        realtime_data.consumed_buy_volume * 10 >= realtime_data.base_ask_volume * 9 ) {
        return true;
    }

    return false;
}

// SZ FCode订阅大单检测函数（基于逐笔委托数据）
inline bool check_sz_fcode_large_order(TickByTickData *tbt, LimitUpSubscription &subscription, SZRealTimeData &realtime_data) {
    if (tbt->type != '0') {
        return false;
    }

    TickByTickEntrust &entrust = tbt->data.entrust;
    if (entrust.price != subscription.limit_up_price) {
        return false;
    }
    // 只处理买入委托（side == '1'）且为增加委托（ord_type == 'A'）
    if (entrust.side != '1' || entrust.ord_type != 'A') {
        return false;
    }

    // 检查是否为大单：vol > 200000
    if (entrust.qty > 200000) {
        // 计算剩余卖量
        int64_t remaining_ask_vol = realtime_data.GetRemainingAskVolume();

        // 检查剩余vol是否少于这个大单
        if (remaining_ask_vol < 2*entrust.qty) {
            snprintf(g_log_buffer, sizeof(g_log_buffer),
                     "SZ FCode大单触发: %s, 大单量: %lld, 剩余卖量: %lld",
                     subscription.symbol, (long long)entrust.qty, (long long)remaining_ask_vol);
            strategy_log(StrategyLogLevel_Info, g_log_buffer);
            return true; // 触发下单条件
        }
    }

    return false;
}


// 高性能日志缓冲系统 - 用于批量处理L2数据日志
#define MAX_LOG_BUFFER_SIZE 65536  // 64KB
#define LOG_BUFFER_THRESHOLD 57344  // 56KB (约87%的缓冲区)

// 预分配的日志缓冲区
static char g_l2_log_buffer[MAX_LOG_BUFFER_SIZE];
static size_t g_l2_log_buffer_pos = 0;  // 当前缓冲区位置
static int64_t g_last_flush_time = 0;   // 上次刷新时间

// 向L2日志缓冲区添加日志
inline void append_l2_log(const char* format, ...) {
    // 检查剩余空间
    if (g_l2_log_buffer_pos >= LOG_BUFFER_THRESHOLD) {
        // 缓冲区接近满，先打印
        strategy_log(StrategyLogLevel_Info, g_l2_log_buffer);
        g_l2_log_buffer_pos = 0;  // 重置缓冲区位置
    }
    
    va_list args;
    va_start(args, format);
    // 直接写入缓冲区，避免中间字符串分配
    int written = vsnprintf(g_l2_log_buffer + g_l2_log_buffer_pos, 
                           MAX_LOG_BUFFER_SIZE - g_l2_log_buffer_pos,
                           format, args);
    va_end(args);
    
    if (written > 0) {
        g_l2_log_buffer_pos += written;
        
        // 添加换行符
        if (g_l2_log_buffer_pos < MAX_LOG_BUFFER_SIZE - 1) {
            g_l2_log_buffer[g_l2_log_buffer_pos++] = '\n';
            g_l2_log_buffer[g_l2_log_buffer_pos] = '\0';
        }
    }
}

// 强制刷新L2日志缓冲区
inline void flush_l2_log_buffer(bool force = false) {
    // 获取当前时间
    struct timeval tv;
    gettimeofday(&tv, NULL);
    int64_t current_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;  // 毫秒
    
    // 如果有内容且(强制刷新或者距离上次刷新超过1秒)
    if (g_l2_log_buffer_pos > 0 && (force || current_time - g_last_flush_time > 1000)) {
        strategy_log(StrategyLogLevel_Info, g_l2_log_buffer);
        g_l2_log_buffer_pos = 0;  // 重置缓冲区位置
        g_last_flush_time = current_time;
    }
}

void OnTickbytickData(TickByTickData *tbt, void *user_data) {
    // 高性能热路径：直接使用char[12]，避免string构造

    // 快速查找订阅索引（优先在紧凑数组中查找）
    if (tbt->data_time < 93000000) {
        return;
    }
    gettimeofday(&tv, NULL);
    start = tv.tv_usec;
    int sub_index = find_subscription_index(tbt->symbol);
    if (sub_index < 0) {
        return; // 未订阅或已暂停，直接返回
    }

    LimitUpSubscription &subscription = g_limitup_subscriptions[sub_index];
    SZRealTimeData &realtime_data = g_sz_realtime_data[sub_index];
    if (!realtime_data.is_ready) {
        return;
    }
    
    // 使用缓冲日志系统记录涨停相关委托成交撤单信息
    // 先区分市场类型，再处理不同市场的委托和成交
    if (subscription.market_type == SH) { // 上海市场
        if (tbt->type == '0') { // 逐笔委托
            TickByTickEntrust &entrust = tbt->data.entrust;
            const char* order_action = "";
            if (entrust.ord_type == 'A') {
                order_action = "BW";
            } else if (entrust.ord_type == 'D') {
                order_action = "CW";
            }
            
            // 上海市场涨停价委托
            if (entrust.price == subscription.limit_up_price && entrust.side == '1' && entrust.qty > 10000) {
                append_l2_log("[%06ld]|%s|%s|V:%lld|T:%lld|N:%lld", 
                             tv.tv_usec, tbt->symbol, order_action, (long long)entrust.qty, tbt->data_time, entrust.order_no);
            }
        } else if (tbt->type == '1') { // 逐笔成交
            TickByTickTrade &trade = tbt->data.trade;
            
            // 上海市场涨停价主动买入成交
            if (trade.price == subscription.limit_up_price && trade.trade_flag == 'B') {
                append_l2_log("[%06ld]|%s|ZB|V:%lld|T:%lld|BN:%lld|SN:%lld",
                             tv.tv_usec, tbt->symbol, (long long)trade.qty, tbt->data_time, 
                             (long long)trade.bid_no, (long long)trade.ask_no);
            }
        }
    } else if (subscription.market_type == SZ) { // 深圳市场
        if (tbt->type == '0') { // 逐笔委托
            TickByTickEntrust &entrust = tbt->data.entrust;
            // 深圳市场涨停价委托
            if (entrust.price == subscription.limit_up_price) {
                if (entrust.side == '1' && entrust.qty > 10000) { // 买入委托
                    append_l2_log("[%06ld]|%s|BW|V:%lld|T:%lld|N:%lld",
                        tv.tv_usec, tbt->symbol, entrust.qty, tbt->data_time,entrust.seq);
                } else if (entrust.side == '2' && entrust.qty > 10000) { // 卖出委托
                    append_l2_log("[%06ld]|%s|SW|V:%lld|T:%lld|N:%lld",
                        tv.tv_usec,tbt->symbol, entrust.qty, tbt->data_time, entrust.seq);
                }
            } else if (entrust.price == subscription.limit_down_price + 200 && entrust.qty == 100 && entrust.side == '1') {
                // 测试单
                append_l2_log("[%06ld]|%s|BW|V:%lld|T:%lld|N:%lld",
                    tv.tv_usec, tbt->symbol, entrust.qty, tbt->data_time, entrust.seq);
            }
        } else if (tbt->type == '1') { // 逐笔成交
            TickByTickTrade &trade = tbt->data.trade;
            // 深圳市场撤单成交 只看买撤
            if (trade.trade_flag == '4' && trade.bid_no != 0) {
                if (trade.qty < 10000) {
                    return;
                }

                append_l2_log("[%06ld]|%s|CW|V:%lld|T:%lld|N:%lld|BN:%lld|SN:%lld|",
                            tv.tv_usec, tbt->symbol, trade.qty, tbt->data_time, trade.seq, trade.bid_no, trade.ask_no);
            }
        }
    }
    
}

void OnIndexTickCallback(IndexTickData *res, void *user_data) {
    //处理指数行情
}

// 市场日期变更回调处理函数
void OnDateUpdate(DateUpdateData *dud, void *user_data) {
    ostringstream oss;
    oss << "OnDateUpdate: market: " << dud->market << ", date: " << dud->date;

    strategy_log(StrategyLogLevel_Info, oss.str().data());
    std::cout << "OnDateUpdate: market: " << dud->market
            << ", date: " << dud->date << endl;
}

// 2. 创建JSON对象示例
//json strategy_config;
//strategy_config["strategy_name"] = "LimitUpStrategy";
//strategy_config["version"] = "1.0.0";
//strategy_config["author"] = "Quant Team";
//
//// 添加订阅配置
//json subscriptions = json::array();
//for (const auto& pair : g_limitup_subscriptions) {
//    json sub_info;
//    sub_info["symbol"] = pair.first;
//    sub_info["threshold"] = pair.second.threshold_volume;
//    sub_info["market"] = (pair.second.market_type == SH) ? "SH" : "SZ";
//    sub_info["active"] = pair.second.is_active;
//    subscriptions.push_back(sub_info);
//}
//strategy_config["subscriptions"] = subscriptions;
//
//// 添加统计信息
//strategy_config["stats"]["total_subscriptions"] = g_limitup_subscriptions.size();
//strategy_config["stats"]["tick_data_count"] = g_latest_ticks.size();
//strategy_config["stats"]["current_time"] = str_datetime_now();
//
//ostringstream config_log;
//config_log << "=== 策略配置JSON ===" << endl;
//config_log << strategy_config.dump(4);
//strategy_log(StrategyLogLevel_Info, config_log.str().data());
// 策略参数设置回调函数
void OnStrategyParamsSetting(const char *params_json, void *user_data) {
    // JSON使用示例
    try {
        // 1. 解析传入的JSON字符串
        json parsed_params = json::parse(params_json);
        // 遍历JSON对象
        if (parsed_params.is_array()) {
            for (size_t i = 0; i < parsed_params.size(); ++i) {
                const auto &item = parsed_params[i];
                if (item.contains("key") && item.contains("value")) {
                    // 处理添加订阅 (Code)
                    if (item["key"] == "Code") {
                        // 新增一个订阅，自动添加市场前缀
                        char symbol[12];
                        code_to_symbol(item["value"], symbol);
                        SubscribeLimitUpDetection(symbol, ************); // 实际是2qw
                    } else if (item["key"] == "FCode") {
                        // 新增一个订阅，自动添加市场前缀
                        char symbol[12];
                        code_to_symbol(item["value"], symbol);
                        SubscribeLimitUpDetection(symbol, 0);
                    } else if (item["key"] == "DCode") {
                        // 删除一个订阅
                        char symbol[12];
                        code_to_symbol(item["value"], symbol);
                        UnsubscribeLimitUpDetection(symbol);
                    } else if (item["key"] == "Log") {
                        if (item["value"] == "1") {
                            test_order = "1";
                        }
                    }
                }
            }
        }
    } catch (const json::exception &e) {
        ostringstream error_log;
        error_log << "JSON处理错误: " << e.what();
        strategy_log(StrategyLogLevel_Info, error_log.str().data());
    }

    // 打印封单检测订阅信息
    ostringstream sub_info;
    sub_info << "=== 封单检测订阅信息 ===" << endl;
    sub_info << "订阅数量: " << g_subscription_count << endl;

    for (int i = 0; i < g_subscription_count; ++i) {
        const LimitUpSubscription &sub = g_limitup_subscriptions[i];
        if (sub.is_active) {
            // 只显示激活的订阅
            sub_info << sub.symbol << ": "
                    << "阈值=" << sub.threshold_volume << "股, "
                    << "状态=" << (sub.is_active ? "激活" : "暂停") << ", "
                    << "涨停价=" << (sub.limit_up_price / 10000.0) << "元" << endl;
        }
    }

    // 打印SZ市场实时数据
    if (g_subscription_count > 0) {
        sub_info << "=== 市场实时数据 ===" << endl;
        sub_info << "数据数量: " << g_subscription_count << endl;

        for (int i = 0; i < g_subscription_count; ++i) {
            if (g_limitup_subscriptions[i].is_active) {
                const char *symbol = g_limitup_subscriptions[i].symbol;
                const SZRealTimeData &sz_data = g_sz_realtime_data[i];

                sub_info << symbol << ": "
                        << "基准卖量=" << sz_data.base_ask_volume << "股, "
                        << "消耗买单=" << sz_data.consumed_buy_volume << "股, ";
            }
        }
    }
    strategy_log(StrategyLogLevel_Info, sub_info.str().data());
}

// 成交回报回调处理函数
void OnTradeReport(const Trade *trade, void *user_data) {

    // ostringstream osstream;
    // osstream << "OnTradeReport: " << endl
    //         << "\tstrategy_id: " << trade->strategy_id << endl
    //         << "\trun_id: " << trade->run_id << endl
    //         << "\torder_id: " << trade->order_id << endl
    //         << "\tcl_order_id: " << trade->cl_order_id << endl
    //         << "\tsymbol: " << trade->symbol << endl
    //         << "\taccount_id: " << trade->account_id << endl
    //         << "\taccount_type: " << trade->account_type << endl
    //         << "\tdate: " << trade->date << endl
    //         << "\ttrade_seqno: " << trade->trade_seqno << endl
    //         << "\tside: " << trade->side << endl
    //         << "\torder_type: " << trade->order_type << endl
    //         << "\texec_type: " << trade->exec_type << endl
    //         << "\texec_id: " << trade->exec_id << endl
    //         << "\tvolume: " << trade->volume << endl
    //         << "\tprice: " << trade->price << endl
    //         << "\tturnover: " << trade->turnover << endl
    //         << "\ttransact_time: " << trade->transact_time;
    // strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

// 委托应答回调处理函数
void OnOrderRsp(const OrderRsp *order_rsp, int count, void *user_data) {
    // 输出委托应答到strategy目录的日志中
    ostringstream osstream;
    for (int i = 0; i < count; ++i) {
        osstream.str("");
        osstream << "OrderRsp: " << endl
                << "\torder_id: " << order_rsp[i].order_id << endl
                << "\tcl_order_id: " << order_rsp[i].cl_order_id << endl
                << "\terr_code: " << order_rsp[i].err_code << endl
                << "\terr_msg: " << order_rsp[i].err_msg;
        strategy_log(StrategyLogLevel_Info, osstream.str().data());
    }
}

// 委托状态回调处理函数
void OnOrderStatus(const Order *res, void *user_data) {

    if (res->side != 1) {
        return;
    }
    
    // 查找对应的订阅索引
    int sub_index = find_subscription_index(res->symbol);
    
    // 检查是否是测试单（volume=100 且 price=跌停价+300）
    if (res->volume == 100) {
        int idx = find_origin_subscription_index(res->symbol);
        if (idx >= 0) {
            int64_t limit_down_price = g_limitup_subscriptions[idx].limit_down_price;
            if (res->price == limit_down_price + 300) {
                // 执行撤单
                CancelDetail *cancelList = NULL;
                int cancelCount = 0;
                int ret = td_cancel_order(kStockAccount, AccountType_Stock, res->order_id,
                                        &cancelList, &cancelCount);
                if (ret != 0) {
                    ostringstream cancel_log;
                    cancel_log << "测试单撤单失败: " << res->symbol
                            << ", 订单号: " << res->order_id
                            << ", 错误码: " << ret
                            << ", 错误信息: " << hft_strerror(ret);
                    strategy_log(StrategyLogLevel_Error, cancel_log.str().data());
                }
            }
        }
    }
    
    // 处理正常订单的完成状态
    if (res->order_status == OrderStatus_Filled || res->order_status == OrderStatus_CancelFilled) {
        if (sub_index >= 0) {
            SZRealTimeData &realtime_data = g_sz_realtime_data[sub_index];

            // 正常成交，重置下单时间（表示订单已完成）
            realtime_data.order_time = 0;
            // 清空订单号
            memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));
            g_limitup_subscriptions[sub_index].is_active = false;

            // 从紧凑数组中移除
            g_active_watching_symbols.remove(sub_index);
        }
    }

    // 输出委托状态到strategy目录的日志中
    ostringstream osstream;
    osstream << "OnOrderStatus: " << endl
            << "\tsymbol: " << res->symbol << endl
            << "\torder_id: " << res->order_id << endl  // 添加订单ID便于跟踪
            << "\tcreate_time: " << res->create_time << endl
            << "\tupdate_time: " << res->update_time << endl
            << "\tprice: " << (res->price / 10000.0) << "元" << endl  // 添加价格便于识别测试单
            << "\torder_status: " << res->order_status << endl
            << "\tvolume: " << res->volume << "股" << endl  // 添加数量便于识别测试单
            << "\tfilled_volume: " << res->filled_volume << endl
            << "\tfilled_turnover: " << res->filled_turnover;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

// 行情服务状态回调
void OnMdStatusChange(int conn_status, void *user_data) {
    ostringstream osstream;
    osstream << "OnMdStatusChange: " << conn_status;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

// 定时回调处理函数
void OnStrategyTimer(int interval, void *user_data) {
    ostringstream osstream;

    // 定时任务中只调用一次get_security_ticks，作为示例
    static bool security_ticks_called = false;
    if (!security_ticks_called) {
        security_ticks_called = true;
        SecurityTickData *sd = NULL;
        int count = 0;
        int ret = get_security_ticks("SH.600000,SZ.000001", "2020-1-2 10:0:0",
                                     "2020-1-2 16:0:0", &sd, &count);

        osstream.str("");
        osstream << "get_security_ticks: " << ret << ", count:" << count
                << ", sd: " << sd;
        strategy_log(StrategyLogLevel_Info, osstream.str().data());


        strategy_log(StrategyLogLevel_Info, osstream.str().data());
    }

    // 定时任务中只调用一次get_security_kdata，作为示例
    static bool security_kdata_called = false;
    if (!security_kdata_called) {
        security_kdata_called = true;
        SecurityKdata *sd = NULL;
        int count = 0;
        int ret = get_security_kdata("SH.600000", "2018/6/1", "2018/6/1",
                                     "1min", "none", &sd, &count);

        osstream.str("");
        osstream << "get_security_kdata: " << ret << ", count:" << count
                << ", sd: " << sd;
        strategy_log(StrategyLogLevel_Info, osstream.str().data());
    }

        if (interval == 5000) {
            // 构建 JSON 数组用于自定义指标
            json indexes;

            // 遍历紧凑数组中的所有活跃股票
            for (int i = 0; i < g_active_watching_symbols.count; ++i) {
                int idx = g_active_watching_symbols.indices[i];
                if (idx >= 0 && idx < g_subscription_count) {
                    const LimitUpSubscription &sub = g_limitup_subscriptions[idx];
                    const SZRealTimeData &data = g_sz_realtime_data[idx];

                    // 为每只股票创建一个指标对象
                    char key[32], name[32];
                    snprintf(key, sizeof(key), "code%d", i + 1);
                    snprintf(name, sizeof(name), "code%d", i + 1);

                    // 构建股票信息字符串
                    char value[256];
                    snprintf(value, sizeof(value), "%s(%lld,%s,%lld)",
                            sub.symbol,
                            (long long)sub.threshold_volume,
                            sub.is_ordered ? "yes" : "no",
                            (long long)data.order_time);

                    // 添加到 JSON 数组
                    json item;
                    item["key"] = key;
                    item["name"] = name;
                    item["type"] = "string";
                    item["value"] = value;
                    indexes.push_back(item);
                }
            }

            // 添加活跃股票数量指标
            json count_item;
            count_item["key"] = "active_count";
            count_item["name"] = "active_count";
            count_item["type"] = "int";
            count_item["value"] = g_active_watching_symbols.count;
            indexes.push_back(count_item);

            // 上报指标
            strategy_report_indexes(indexes.dump().c_str());
        }

}

void OnDayTaskCallback(int time, void *user_data) {
    ostringstream osstream;
    std::cout << "OnDayTaskCallback: " << time << std::endl;

    orderCount = 0; //置0，让当天又可以下单交易
    if (g_orderids.size() > 0)
        g_orderids.clear();

    osstream << "OnDayTaskCallback: " << time;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

void OnExit(int reason, void *user_data) {
    // 强制刷新所有缓存的日志
    flush_l2_log_buffer(true);
    
    // 退出回调做一些资源回收等最终操作
    ostringstream osstream;
    osstream << "OnExit, reason: " << strategy_exit_reason(reason);
    const string &message = osstream.str();
    cout << message << endl;
    strategy_log(StrategyLogLevel_Info, message.data());
    std::cout << "OnExit, reason: " << strategy_exit_reason(reason);
}

#ifdef _WIN32
bool CtrlHandler(DWORD fdwctrltype) {
    switch (fdwctrltype) {
        // handle the ctrl-c signal.
        case CTRL_C_EVENT:
            strategy_exit();
            return (true);

        default:
            return false;
    }
}
#endif

// 委托队列数据回调处理函数
void OnOrderQueue(OrderQueueData *res, void *user_data) {
    // 高性能查找订阅
    int sub_index = find_subscription_index(res->symbol);
    if (sub_index >= 0 && g_limitup_subscriptions[sub_index].is_active) {
        // 找到了订阅的股票，打印委托队列信息
        ostringstream log;
        log << "=== 委托队列数据 ===" << endl
                << "证券代码: " << res->symbol << endl;

        // 遍历两档委托队列数据
        for (int i = 0; i < 2; i++) {
            const OrderQueueItemData &item = res->item[i];
            log << "档位 " << (i + 1) << ":" << endl
                    << "  委托时间: " << item.time << endl
                    << "  买卖方向: " << (item.side == 'B' ? "买入" : "卖出") << endl
                    << "  委托价格: " << (item.price / 10000.0) << " 元" << endl
                    << "  委托数量: " << item.order_num << endl
                    << "  委托明细数量: " << item.item_num << endl;

            // 打印委托明细（最多显示前10个）
            log << "  委托明细: ";
            int display_count = item.item_num;
            for (int j = 0; j < display_count; j++) {
                log << item.volume[j];
                if (j < display_count - 1) {
                    log << ", ";
                }
            }
            // if (item.item_num > 10) {
            //     log << ", ... (还有" << (item.item_num - 10) << "条)";
            // }
            log << endl;
        }

        strategy_log(StrategyLogLevel_Info, log.str().data());
    }
}

// 获取可执行文件所在路径，保留结尾的'\'或'/'符
std::string get_exe_path() {
    const size_t max_path = 256;
    std::string s;
#ifdef _WIN32
    TCHAR file_path[max_path + 1];
    size_t count = GetModuleFileName(NULL, file_path, max_path);
    if (0 == count) {
        strategy_log(StrategyLogLevel_Error, "GetModuleFileName failed");
        return s; // empty
    }
    s = file_path;
    size_t pos = s.find_last_of('\\');
    strategy_log(StrategyLogLevel_Info, ("Windows路径: " + s).c_str());
    return s.substr(0, pos+1);
#else
    char file_path[max_path];
    size_t count = readlink("/proc/self/exe", file_path, max_path);
    if (count <= 0) {
        strategy_log(StrategyLogLevel_Error, "readlink failed, error: ");
        return "./"; // 返回当前目录
    }
    file_path[count] = '\0';
    s = file_path;
    size_t pos = s.find_last_of('/');
    std::string path = s.substr(0, pos+1);
    strategy_log(StrategyLogLevel_Info, ("Linux路径: " + path).c_str());
    return path;
#endif
}

// 读取JSON文件并订阅个股
void read_and_subscribe_stocks_from_json() {
    // 获取可执行文件所在路径
    std::string exe_path = get_exe_path();
    std::string json_path = exe_path + "up.json";
    
    strategy_log(StrategyLogLevel_Info, ("尝试读取JSON文件: " + json_path).c_str());
    
    // 检查文件是否存在
    std::ifstream check_file(json_path.c_str());
    if (!check_file.good()) {
        snprintf(g_log_buffer, sizeof(g_log_buffer), "文件不存在或无法访问: %s", json_path.c_str());
        strategy_log(StrategyLogLevel_Error, g_log_buffer);
        
        // 尝试在当前工作目录查找
        json_path = "./up.json";
        strategy_log(StrategyLogLevel_Info, ("尝试在当前目录读取: " + json_path).c_str());
        check_file.close();
        check_file.open(json_path.c_str());
        if (!check_file.good()) {
            snprintf(g_log_buffer, sizeof(g_log_buffer), "当前目录也找不到文件: %s", json_path.c_str());
            strategy_log(StrategyLogLevel_Error, g_log_buffer);
            return;
        }
    }
    check_file.close();
    
    // 打开JSON文件
    std::ifstream file(json_path);
    if (!file.is_open()) {
        snprintf(g_log_buffer, sizeof(g_log_buffer), "无法打开JSON文件: %s", json_path.c_str());
        strategy_log(StrategyLogLevel_Error, g_log_buffer);
        return;
    }
    
    strategy_log(StrategyLogLevel_Info, "成功打开JSON文件，开始读取内容");
    
    try {
        // 读取文件内容到字符串
        std::stringstream buffer;
        buffer << file.rdbuf();
        std::string json_str = buffer.str();
        file.close();
        
        if (json_str.empty()) {
            strategy_log(StrategyLogLevel_Error, "JSON文件内容为空");
            return;
        }
        
        // 记录文件内容的前100个字符（用于调试）
        std::string preview = json_str.substr(0, std::min(size_t(100), json_str.size()));
        snprintf(g_log_buffer, sizeof(g_log_buffer), "JSON内容预览: %s...", preview.c_str());
        strategy_log(StrategyLogLevel_Info, g_log_buffer);
        
        // 解析JSON内容，使用与OnStrategyParamsSetting相同的处理方式
        strategy_log(StrategyLogLevel_Info, "开始解析JSON");
        json parsed_json = json::parse(json_str);
        strategy_log(StrategyLogLevel_Info, "JSON解析成功");
        
        // 记录订阅的股票总数
        int total_stocks = 0;
        
        // 记录JSON中的板块数量
        snprintf(g_log_buffer, sizeof(g_log_buffer), "JSON包含 %zu 个板块", parsed_json.size());
        strategy_log(StrategyLogLevel_Info, g_log_buffer);
        
        // 遍历JSON中的每个板块
        for (auto it = parsed_json.begin(); it != parsed_json.end(); ++it) {
            const std::string& sector = it.key();
            const json& sector_data = it.value();
            
            // 获取该板块的原因和股票代码列表
            std::string reason = "";
            if (sector_data.contains("reason") && !sector_data["reason"].is_null()) {
                reason = sector_data["reason"].get<std::string>();
            }
            
            if (!sector_data.contains("codes") || !sector_data["codes"].is_array()) {
                snprintf(g_log_buffer, sizeof(g_log_buffer), "板块 %s 缺少有效的codes数组", sector.c_str());
                strategy_log(StrategyLogLevel_Info, g_log_buffer);
                continue;
            }
            
            const json& codes = sector_data["codes"];
            
            // 记录日志：板块信息
            snprintf(g_log_buffer, sizeof(g_log_buffer), "板块: %s, 原因: %s, 股票数量: %zu", 
                    sector.c_str(), reason.c_str(), codes.size());
            strategy_log(StrategyLogLevel_Info, g_log_buffer);
            
            // 遍历该板块的所有股票代码
            for (const auto& code : codes) {
                if (!code.is_string()) {
                    strategy_log(StrategyLogLevel_Info, "跳过非字符串股票代码");
                    continue;
                }
                
                std::string stock_code = code.get<std::string>();
                
                // 转换股票代码为符合系统要求的格式
                char symbol[12];
                code_to_symbol(stock_code, symbol);
                
                // 订阅该股票
                SubscribeLimitUpDetection(symbol, 0);  // 使用0阈值，表示使用FCode模式
                total_stocks++;
                
                // 记录日志：订阅的股票
                snprintf(g_log_buffer, sizeof(g_log_buffer), "已订阅股票: %s (原始代码: %s)", 
                        symbol, stock_code.c_str());
                strategy_log(StrategyLogLevel_Info, g_log_buffer);
            }
        }
        
        // 记录订阅总数
        snprintf(g_log_buffer, sizeof(g_log_buffer), "总共订阅了 %d 只股票", total_stocks);
        strategy_log(StrategyLogLevel_Info, g_log_buffer);
        
    } catch (const json::exception& e) {
        snprintf(g_log_buffer, sizeof(g_log_buffer), "JSON解析错误: %s", e.what());
        strategy_log(StrategyLogLevel_Error, g_log_buffer);
    } catch (const std::exception& e) {
        snprintf(g_log_buffer, sizeof(g_log_buffer), "读取文件错误: %s", e.what());
        strategy_log(StrategyLogLevel_Error, g_log_buffer);
    }
}

int main(int argc, const char *argv[]) {
    // 处理命令行CTRL-C退出信号
#ifdef _WIN32
    if (!SetConsoleCtrlHandler((PHANDLER_ROUTINE) CtrlHandler, TRUE)) {
        cout << "ERROR: Could not set console control handler" << endl;
        strategy_log(StrategyLogLevel_Error,
                     "ERROR: Could not set console control handler");
        return 1;
    }
#endif
    // 初始化策略
    ostringstream osstream;
    int rc = strategy_init();
    int mode = 0;

    if (0 != rc) {
        osstream << "strategy_init failed: " << rc
                << ", msg: " << hft_strerror_utf8(rc);
        cout << "strategy_init failed: " << rc
                << ", msg: " << hft_strerror(rc) << endl;
        strategy_log(StrategyLogLevel_Error, osstream.str().data());
        goto OnProgramExit;
    }

    // 初始化紧凑数组和位图
    g_active_watching_symbols.clear();

    osstream << "strategy_init success" << endl;
    osstream << "LimitUpSubscription size: " << sizeof(LimitUpSubscription) << endl;
    osstream << "SzRealTimeData size: " << sizeof(SZRealTimeData) << endl;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());
    
    // 读取JSON文件并订阅股票
    read_and_subscribe_stocks_from_json();

    // 设置交易相关回调事件处理函数
    td_set_trade_report_callback(OnTradeReport, NULL);
    td_set_order_rsp_callback(OnOrderRsp, NULL);
    td_set_order_status_callback(OnOrderStatus, NULL);

    // 设置实时行情回调事件处理函数
    md_set_status_change_callback(OnMdStatusChange, NULL);

    // 设置行情回调函数
    md_set_security_tick_callback(OnSecurityTick, NULL); //证券tick行情回调
    md_set_index_tick_callback(OnIndexTickCallback, NULL);
    md_set_security_kdata_callback(OnSecurityKdata, NULL); //证券分钟K线回调
    md_set_tickbytick_callback(OnTickbytickData, NULL);
    // md_set_orderqueue_callback(OnOrderQueue, NULL);  // 委托队列数据回调

    md_set_date_update_callback(OnDateUpdate, NULL); //日期变化回调

    // 设置定时任务回调处理函数
    strategy_set_timer_callback(OnStrategyTimer, NULL);

    // 设置策略退出回调处理函数
    strategy_set_exit_callback(OnExit, NULL);

    // 设置一个5s定时任务
    strategy_set_timer(5000);

    strategy_set_day_schedule_task_callback(OnDayTaskCallback, NULL);
    strategy_set_day_schedule_task(85000);

    // 订阅一个Tick，多个K线行情
    // md_subscribe("SH.601211.tick,SH.601211.bar,SH.000001.index");

    strategy_set_params_setting_callback(OnStrategyParamsSetting, NULL);


    char myparam[512];
    snprintf(myparam, sizeof(myparam),
             "["
             "{\"key\":\"Code\", \"name\":\"Code\", \"type\":\"string\", \"value\":\"SH.600002\"},"
             "{\"key\":\"Log\", \"name\":\"Log\", \"type\":\"string\", \"value\":\"0\"},"
             "{\"key\":\"FCode\", \"name\":\"FCode\", \"type\":\"string\", \"value\":\"SH.600002\"},"

             "{\"key\":\"DCode\", \"name\":\"DCode\", \"type\":\"string\", \"value\":\"SH.\"}"

             "]");
    strategy_report_params(myparam);

    init_order_req();


    // 运行策略
    // mode 0 - 默认模式, 事件触发
    // 1 - spin模式，通过死循环检测事件队列中是否有新的事件到达。
    rc = strategy_run(1);
    if (rc != 0) {
        osstream.str("");
        osstream << "strategy_run failed: " << rc
                << ", msg: " << hft_strerror_utf8(rc);
        cout << "strategy_run failed: " << rc
                << ", msg: " << hft_strerror(rc) << endl;
        strategy_log(StrategyLogLevel_Error, osstream.str().data());
    } else {
        cout << "strategy_run success" << endl;
        strategy_log(StrategyLogLevel_Info, "strategy_run success");
    }

    // 主线程等待策略线程退出
    while (strategy_get_exec_status() != StrategyExecStatus_Term) {
        // sleep 500ms
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

OnProgramExit:
    if (strategy_get_exec_status() != StrategyExecStatus_Term) {
        rc = strategy_exit();
        if (rc != 0) {
            osstream.str("");
            osstream << "strategy_exit failed: " << rc
                    << ", msg: " << hft_strerror_utf8(rc);
            cout << "strategy_exit failed: " << rc
                    << ", msg: " << hft_strerror(rc) << endl;
            strategy_log(StrategyLogLevel_Info, osstream.str().data());
        } else {
            cout << "strategy_exit success" << endl;
            strategy_log(StrategyLogLevel_Info, "strategy_exit success");
        }
    }
    return 0;
}
