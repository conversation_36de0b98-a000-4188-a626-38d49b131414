#ifdef _WIN32
#include <Windows.h>
#endif
#include <sys/time.h>
#include <time.h>
#include <string.h>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>
#include <map>
#include <unordered_map>
#include <algorithm>
#include <chrono>
#include <thread>
#include <cstdint>
#include <fstream>
#include <dirent.h>  // 用于目录遍历
#ifndef _WIN32
#include <unistd.h>  // 用于 readlink
#ifdef __APPLE__
#include <mach-o/dyld.h>  // 用于 _NSGetExecutablePath
#endif
#endif

#ifdef _WIN32
#include <intrin.h>
#elif defined(__x86_64__) || defined(__i386__)
#include <x86intrin.h>
#endif

#include "md_api.h"
#include "strategy_api.h"
#include "trade_api.h"
#include "include/nlohmann/json.hpp"

using namespace std;
using json = nlohmann::json;

const char *kStockAccount = "567212";
const char *kSHProductId = "SH.601211";

// 高性能股票代码管理系统
constexpr size_t MAX_SYMBOLS = 64; // 最多支持64只股票
constexpr size_t SYMBOL_SIZE = 12; // 股票代码固定12字节
struct timeval tv;
long long start;
string test_order = "0";

int orderCount = 0;
CancelDetail *cancelList = NULL;
int cancelCount = 0;
static char *symbol_for_subscribe = NULL;

// 全局日志缓冲区，用于高性能日志打印，避免每次分配内存
static char g_log_buffer[512];


// 极速股票代码比较（3ns延时）
inline bool is_symbol_match(const char symbol1[SYMBOL_SIZE], const char symbol2[SYMBOL_SIZE]) {
    // 使用64位+32位比较，覆盖全部12字节
    const uint64_t *sym1_64 = reinterpret_cast<const uint64_t *>(symbol1);
    const uint64_t *sym2_64 = reinterpret_cast<const uint64_t *>(symbol2);

    // 比较前8字节
    if (sym1_64[0] != sym2_64[0]) return false;

    // 比较后4字节
    const uint32_t *sym1_32 = reinterpret_cast<const uint32_t *>(symbol1 + 8);
    const uint32_t *sym2_32 = reinterpret_cast<const uint32_t *>(symbol2 + 8);
    return sym1_32[0] == sym2_32[0];
}


// 安全地将string转换为char[12]格式
inline void string_to_symbol(const string &str, char symbol[12]) {
    memset(symbol, 0, 12); // 先清零
    size_t len = str.length();
    if (len > 11) len = 11; // 最多复制11字节，留1字节给'\0'
    memcpy(symbol, str.c_str(), len);
}

// 自动添加市场前缀的转换函数
inline void code_to_symbol(const string &code, char symbol[12]) {
    memset(symbol, 0, 12);

    if (code.length() == 6) {
        // 纯数字代码，自动添加前缀
        if (code[0] == '6') {
            // 6开头 -> SH.
            memcpy(symbol, "SH.", 3);
            memcpy(symbol + 3, code.c_str(), 6);
        } else if (code[0] == '0' || code[0] == '3') {
            // 0或3开头 -> SZ.
            memcpy(symbol, "SZ.", 3);
            memcpy(symbol + 3, code.c_str(), 6);
        } else {
            // 其他情况，直接复制
            size_t len = code.length();
            if (len > 11) len = 11;
            memcpy(symbol, code.c_str(), len);
        }
    } else {
        // 已有前缀或其他格式，直接复制
        size_t len = code.length();
        if (len > 11) len = 11;
        memcpy(symbol, code.c_str(), len);
    }
}


// 股票代码到名称的映射
static std::unordered_map<std::string, std::string> g_code_name_map;


// 读取 name.txt 文件，构建股票代码到名称的映射
void read_code_name() {
    std::ifstream file("name.txt");
    if (!file.is_open()) {
        strategy_log(StrategyLogLevel_Info, "无法打开 name.txt 文件");
        return;
    }

    std::string line;
    int loaded_count = 0;

    while (std::getline(file, line)) {
        // 跳过空行
        if (line.empty()) {
            continue;
        }

        // 查找第一个空格的位置
        size_t space_pos = line.find(' ');
        if (space_pos != std::string::npos) {
            std::string code = line.substr(0, space_pos);
            std::string name = line.substr(space_pos + 1);

            // 去除名称前后的空白字符
            name.erase(0, name.find_first_not_of(" \t\r\n"));
            name.erase(name.find_last_not_of(" \t\r\n") + 1);

            if (!code.empty() && !name.empty()) {
                // 使用现有的 code_to_symbol 函数来添加市场前缀
                char symbol[12];
                code_to_symbol(code, symbol);

                g_code_name_map[std::string(symbol)] = name;
                loaded_count++;
            }
        }
    }

    file.close();

    snprintf(g_log_buffer, sizeof(g_log_buffer),
             "成功加载 %d 个股票代码名称映射: %s", loaded_count, g_code_name_map["SH.603373"]);
    strategy_log(StrategyLogLevel_Info, g_log_buffer);
}

// 根据股票代码获取股票名称
std::string get_stock_name(const std::string& code) {
    auto it = g_code_name_map.find(code);
    if (it != g_code_name_map.end()) {
        return it->second;
    }
    return code; // 如果找不到名称，返回代码本身
}

// todo 1. 2阶段下单。
// todo 2. 测试单
// todo 3. 撤单能力
// 下单请求 - 声明为全局变量
static OrderReq req; // 零初始化所有成员

// 在函数中设置其他值
void init_order_req() {
    memset(&req, 0x00, sizeof(OrderReq));

    req.order_type = OrderType_LMT;
    req.side = OrderSide::OrderSide_Bid;
    req.hedge_flag = HedgeFlag::HedgeFlag_Placeholder;
}

// 紧凑数组结构 - 只存储活跃且需要监控的股票索引
struct CompactArray {
    int indices[64]; // 存储原始数组的索引
    int count; // 当前紧凑数组中的元素数量

    CompactArray() : count(0) {
        memset(indices, -1, sizeof(indices));
    }

    // 清空数组
    void clear() {
        count = 0;
        memset(indices, -1, sizeof(indices));
    }

    // 添加索引
    void add(int index) {
        if (count < 64 && index >= 0) {
            indices[count++] = index;
        }
    }

    // 移除指定索引
    void remove(int index) {
        for (int i = 0; i < count; ++i) {
            if (indices[i] == index) {
                // 将最后一个元素移到要删除的位置
                indices[i] = indices[count - 1];
                indices[count - 1] = -1;
                --count;
                break;
            }
        }
    }

    // 检查是否包含某个索引
    bool contains(int index) const {
        for (int i = 0; i < count; ++i) {
            if (indices[i] == index) {
                return true;
            }
        }
        return false;
    }
};

// 全局紧凑数组 - 只包含活跃且需要监控的股票
static CompactArray g_active_watching_symbols;

// 股票代码存储结构
struct SymbolEntry {
    char symbol[SYMBOL_SIZE];
    bool is_active;
    uint32_t hash; // 预计算的哈希值

    SymbolEntry() : is_active(false), hash(0) {
        memset(symbol, 0, SYMBOL_SIZE);
    }
};

// 全局股票代码表
static SymbolEntry g_symbol_table[MAX_SYMBOLS];
static size_t g_symbol_count = 0;


#ifdef _WIN32
#define strncpy strncpy_s
#endif

typedef struct t_Orderid {
    int ntime; //下单行情时间
    int res; //保留
    char sorderid[32]; //委托号
} T_ORDERID;

vector<T_ORDERID> g_orderids;

// 预分配固定大小数组，避免动态内存分配
constexpr size_t MAX_ORDER_PAIRS = 100; // 最多支持100个订单关联
static size_t g_order_pair_count = 0;

// 全局map，保存每个股票代码对应的最新tick数据
map<string, SecurityTickData> g_latest_ticks;
// 事先分配 - 涨停下单执行情况

string log_config = "0";
double limit_up_price_allo = 0.0;

// 市场类型枚举
enum MarketType {
    SH = 0, // 上海市场
    SZ = 1 // 深圳市场
};

// 封单检测订阅信息（支持SH/SZ双市场）- 缓存行对齐优化
struct alignas(64) LimitUpSubscription {
    char symbol[12]; // 股票代码，使用char[12]避免动态分配
    MarketType market_type; // 市场类型
    int64_t threshold_volume; // 封单阈值（股数，SH直接比较；SZ用于计算金额）
    bool is_active; // 是否激活
    bool is_ordered; // 是否已经下单
    uint64_t limit_up_price; // 涨停价（用于快速比较）
    uint64_t limit_down_price; // 跌停价
    uint64_t threshold_price; // 7%涨幅价格阈值
    uint64_t pre_close_price; // 前收盘价
    // 填充至64字节
    uint8_t padding[9]; // 18字节填充（调整了填充大小）

    LimitUpSubscription() : market_type(SH), threshold_volume(0), is_active(false), is_ordered(false),
                            limit_up_price(0), limit_down_price(0), threshold_price(0), pre_close_price(0) {
        memset(symbol, 0, 12);
        memset(padding, 0, sizeof(padding));
    }
};


// SH大买单跟踪结构（简化版：只跟踪最新的一个）
struct SHLargeBuyOrder {
    int64_t bid_no;           // 买方订单号
    int64_t total_qty;        // 累计成交量
    int64_t last_update_time; // 最后更新时间

    SHLargeBuyOrder() : bid_no(0), total_qty(0), last_update_time(0) {}
};

// SZ市场专用的实时数据 - 缓存行对齐优化
struct alignas(64) SZRealTimeData {
    // 数据成员
    int64_t base_ask_volume; // 基准总卖量（来自tick的total_ask_vol）
    int64_t consumed_buy_volume; // 3秒内消耗的涨停价买单量
    int64_t initial_seal_volume; // 初始封单量（ask1_vol）
    int64_t current_seal_volume; // 当前封单量
    int64_t order_time; // 如果下单了，下单时间，否则0
    int64_t last_seal_volume;
    // 新增：成交金额风控相关字段
    int64_t order_start_turnover; // 下单时的总成交金额，用于计算期间成交量
    int64_t last_cancel_time; // 上次撤单时间，用于慢排版10秒时间窗口判断
    // 新增：提前扫板成交额限制字段
    int64_t current_turnover; // 当前总成交额，用于提前扫板1亿成交额判断

    // 新增：跨tick封单跟踪字段（10秒内20%撤单能力）
    int64_t seal_volume_history[4]; // 循环存储最近4个tick的封单量
    int64_t tick_time_history[4]; // 循环存储最近4个tick的时间
    int tick_index; // 当前tick在数组中的索引（0-3循环）
    int tick_count; // 总tick计数，用于判断是否有足够历史数据

    // 简化的300ms滑动窗口：3个100ms槽位
    static const int WINDOW_SLOTS = 3;   // 3个时间槽，每个100ms
    int64_t volume_slots[WINDOW_SLOTS];  // [0]=最新100ms, [1]=中间100ms, [2]=最旧100ms
    int64_t slot_start_times[WINDOW_SLOTS]; // 每个槽位的起始时间
    int current_slot;                    // 当前活跃槽位索引

    // 初始化简化滑动窗口
    void initSlidingWindow() {
        memset(volume_slots, 0, sizeof(volume_slots));
        memset(slot_start_times, 0, sizeof(slot_start_times));
        current_slot = 0;
    }

    // 简化的滑动窗口更新（真正的O(1)）
    void addVolumeToSlidingWindow(int64_t timestamp, int64_t volume) {
        // 对齐到100ms边界
        int64_t slot_time = (timestamp / 100) * 100;

        // 检查是否需要切换到新的100ms槽位
        if (slot_start_times[current_slot] == 0) {
            // 初始化第一个槽位
            slot_start_times[current_slot] = slot_time;
        } else if (slot_time > slot_start_times[current_slot]) {
            // 时间跨越到新的100ms，切换槽位
            switchToNextSlot(slot_time);
        }

        // 累积到当前槽位
        volume_slots[current_slot] += volume;
    }

    // 切换到下一个100ms槽位
    void switchToNextSlot(int64_t new_slot_time) {
        // 移动到下一个槽位（循环数组）
        current_slot = (current_slot + 1) % WINDOW_SLOTS;

        // 清空新槽位并设置时间
        volume_slots[current_slot] = 0;
        slot_start_times[current_slot] = new_slot_time;
    }

    // 大单触发检测：只在大单出现时计算300ms累积
    bool checkLargeOrderTrigger(int64_t volume, int64_t price, int64_t remaining_ask_volume) {
        // 计算当前单子的金额（元）
        // price已放大10000倍，所以 volume * price / 10000 = 实际金额（元）
        int64_t order_amount = (volume * price) / 10000;

        // 只有大单（>=3000万元）才触发检测
        if (order_amount < 30000000) {  // 3000万元
            return false;
        }

        // 计算300ms内的总成交量
        int64_t total_volume_300ms = calculate300msVolume();

        // 检查条件：300ms内累积量>=2000万 且 剩余封单<200万
        return total_volume_300ms * price >= 100000000000 && remaining_ask_volume*price < 20000000000;
    }

    // 计算300ms内的总成交量
    int64_t calculate300msVolume() {
        int64_t total = 0;

        // 累加3个100ms槽位的成交量
        for (int i = 0; i < WINDOW_SLOTS; i++) {
            total += volume_slots[i];
        }

        return total;
    }



    // 检查300ms滑动窗口是否满足条件（简化版本）
    bool checkSlidingWindow300ms(int64_t volume, int64_t price, int64_t remaining_ask_volume) {
        return checkLargeOrderTrigger(volume, price, remaining_ask_volume);
    }

    // 打板模式状态跟踪
    bool has_hit_limit_up;           // 是否曾经涨停过
    int64_t last_break_time;         // 上次断板时间
    bool is_currently_limit_up;      // 当前是否处于涨停状态
    int64_t first_limit_up_time;     // 第一次涨停时间

    // 初始化打板状态
    void initLimitUpTracking() {
        has_hit_limit_up = false;
        last_break_time = 0;
        is_currently_limit_up = false;
        first_limit_up_time = 0;
    }

    // 计算打板模式的动态阈值
    int64_t calculateDynamicThreshold(int64_t current_time) {
        // 模式1：932之前没上过板，阈值2000万
        if (!has_hit_limit_up && current_time < 93200000) {
            return ************;  // 2000万
        }

        // 模式2：932之后没上板，阈值0
        if (!has_hit_limit_up && current_time >= 93200000) {
            return 0;
        }

        // 模式3：非第一次上板，距离上次断板<1分钟，阈值2000万
        if (has_hit_limit_up && last_break_time > 0 &&
            (current_time - last_break_time) < 100000) {  // 60秒 = 60000ms
            return ************;  // 2000万
        }

        // 模式4：非第一次上板，距离上次断板≥1分钟，阈值0
        if (has_hit_limit_up && last_break_time > 0 &&
            (current_time - last_break_time) >= 100000) {
            return 0;
        }

        // 默认情况：第一次上板但还没断板，使用保守阈值
        return ************;  // 2000万
    }

    char order_id[32]; // 订单号，字符串格式
    bool risk_control_triggered; // 是否已触发风控撤单
    int64_t slow_mode; // 慢排版模式开启时间戳，0表示未开启
    bool is_limit_up; // 是否涨停状态
    bool is_ready;
    bool has_sweep_order; // 标识是否有扫板情况（涨幅小于9点）
    // SH大买单跟踪（只跟踪最新的一个）
    SHLargeBuyOrder sh_current_large_order;

    // 新增：板块监控索引字段
    int sector_group_index; // 所属板块分组索引，-1表示不属于任何分组
    // 填充至64字节

    uint16_t num_order0; // 0号订单数量
    uint16_t num_order10; // 1号订单数量
    uint16_t num_order20; // 2号订单数量
    uint16_t num_order30; // 3号订单数量
    uint16_t num_order40; // 4号订单数量
    uint16_t num_order50; // 5号订单数量
    uint16_t num_order60; // 6号订单数量
    uint8_t padding2[22]; // 调整填充大小，减少4字节以适应新增的sector_group_index

    // 计算剩余卖量
    int64_t GetRemainingAskVolume() const {
        return base_ask_volume - consumed_buy_volume;
    }

    // 更新SH大买单（简化版：只跟踪最新的一个）
    void UpdateSHLargeBuyOrder(int64_t bid_no, int64_t qty, int64_t update_time) {
        // 如果是同一个买方订单号，累加成交量
        if (sh_current_large_order.bid_no == bid_no) {
            sh_current_large_order.total_qty += qty;
            sh_current_large_order.last_update_time = update_time;
        } else {
            // 如果是新的买方订单号，切换到新的大买单
            sh_current_large_order.bid_no = bid_no;
            sh_current_large_order.total_qty = qty;
            sh_current_large_order.last_update_time = update_time;
        }
    }

    // 检查当前大买单是否满足条件
    bool CheckSHLargeBuyOrderCondition() const {
        // 检查当前大买单是否超过30万股（300000）
        if (sh_current_large_order.total_qty > 300000) {
            // 检查剩余卖量是否不足这个大买单的量
            int64_t remaining_ask_vol = GetRemainingAskVolume();
            if (remaining_ask_vol < sh_current_large_order.total_qty) {
                return true;
            }
        }
        return false;
    }
};

// 更新打板状态跟踪的辅助函数
void updateLimitUpTracking(SZRealTimeData &realtime_data, bool is_limit_up, int64_t current_time) {
    // 检测涨停状态变化
    if (is_limit_up && !realtime_data.is_currently_limit_up) {
        // 从非涨停变为涨停
        if (!realtime_data.has_hit_limit_up) {
            // 第一次涨停
            realtime_data.has_hit_limit_up = true;
            realtime_data.first_limit_up_time = current_time;
        }
        realtime_data.is_currently_limit_up = true;
    } else if (!is_limit_up && realtime_data.is_currently_limit_up) {
        // 从涨停变为非涨停（断板）
        realtime_data.is_currently_limit_up = false;
        realtime_data.last_break_time = current_time;
    }
}

// 基本监听L2，会有一层过滤，只有涨幅大于7的才会进入L1过滤
constexpr size_t MAX_SUBSCRIPTIONS = 64; // 最多支持32只股票
static LimitUpSubscription g_limitup_subscriptions[MAX_SUBSCRIPTIONS];
static SZRealTimeData g_sz_realtime_data[MAX_SUBSCRIPTIONS];
static int g_subscription_count = 0;

// 板块监控相关数据结构
constexpr size_t MAX_SECTOR_GROUPS = 30; // 最多支持10个板块分组
constexpr size_t MAX_CODES_PER_GROUP = 400; // 每个分组最多50只股票

// 涨跌停幅度类型枚举
enum LimitType {
    LIMIT_10CM = 0,  // 10%涨跌停（主板股票）
    LIMIT_20CM = 1   // 20%涨跌停（创业板、科创板）
};

// 板块分组结构（增强版本）
struct SectorGroup {
    char group_name[32]; // 分组名称
    char reason[128]; // 板块热点原因
    char codes[MAX_CODES_PER_GROUP][12]; // 股票代码数组
    int code_count; // 当前分组中的股票数量
    bool is_active; // 是否激活监控
    char ordered_symbol[16]; // 记录已下单的股票代码，空字符串表示未下单
    LimitType limit_type; // 涨跌停幅度类型

    SectorGroup() : code_count(0), is_active(false), limit_type(LIMIT_10CM) {
        memset(group_name, 0, sizeof(group_name));
        memset(reason, 0, sizeof(reason));
        memset(codes, 0, sizeof(codes));
        memset(ordered_symbol, 0, sizeof(ordered_symbol));
    }
};

// 全局板块分组数组
static SectorGroup g_sector_groups[MAX_SECTOR_GROUPS];
static int g_sector_group_count = 0;

// 涨幅股票位图存储系统
constexpr size_t MAX_STOCK_CODES = 1000000; // 支持000000-999999的所有6位数字
constexpr size_t BITMAP_WORDS = (MAX_STOCK_CODES + 63) / 64; // 需要的uint64_t数量

// 位图数组：1000000位需要15625个uint64_t，约125KB
static uint64_t g_gain_stock_bitmap[BITMAP_WORDS];

// 删除g_gain_stocks相关数据结构，简化逻辑

// 高性能股票代码比较函数
inline bool is_symbol_equal(const char symbol1[12], const char symbol2[12]) {
    // 使用64位+32位比较，覆盖全部12字节，延时约3ns
    const uint64_t *sym1_64 = reinterpret_cast<const uint64_t *>(symbol1);
    const uint64_t *sym2_64 = reinterpret_cast<const uint64_t *>(symbol2);

    // 比较前8字节
    if (sym1_64[0] != sym2_64[0]) return false;

    // 比较后4字节
    const uint32_t *sym1_32 = reinterpret_cast<const uint32_t *>(symbol1 + 8);
    const uint32_t *sym2_32 = reinterpret_cast<const uint32_t *>(symbol2 + 8);
    return sym1_32[0] == sym2_32[0];
}
inline int symbol_to_int(const char symbol[12]) {
    const char* code = symbol + 3;

    // 快速验证所有字符都是数字
    if (code[0] < '0' || code[0] > '9' ||
        code[1] < '0' || code[1] > '9' ||
        code[2] < '0' || code[2] > '9' ||
        code[3] < '0' || code[3] > '9' ||
        code[4] < '0' || code[4] > '9' ||
        code[5] < '0' || code[5] > '9') {
        return -1;
        }

    // 直接计算索引，展开循环
    return (code[0] - '0') * 100000 +
           (code[1] - '0') * 10000 +
           (code[2] - '0') * 1000 +
           (code[3] - '0') * 100 +
           (code[4] - '0') * 10 +
           (code[5] - '0');
}
// 设置位图中的某一位
inline void set_gain_stock_bit(int index) {
    if (index < 0 || index >= MAX_STOCK_CODES) return;
    int word_index = index / 64;
    int bit_index = index % 64;
    g_gain_stock_bitmap[word_index] |= (1ULL << bit_index);
}

// 检查位图中的某一位
inline bool is_gain_stock_set(int index) {
    if (index < 0 || index >= MAX_STOCK_CODES) return false;
    int word_index = index / 64;
    int bit_index = index % 64;
    return (g_gain_stock_bitmap[word_index] & (1ULL << bit_index)) != 0;
}

// 清除位图中的某一位
inline void clear_gain_stock_bit(int index) {
    if (index < 0 || index >= MAX_STOCK_CODES) return;
    int word_index = index / 64;
    int bit_index = index % 64;
    g_gain_stock_bitmap[word_index] &= ~(1ULL << bit_index);
}

// 清空整个位图
inline void clear_all_gain_stocks() {
    memset(g_gain_stock_bitmap, 0, sizeof(g_gain_stock_bitmap));
}

// 查找订阅索引（热路径优化）- 使用紧凑数组加速
inline int find_origin_subscription_index(const char symbol[12]) {
    for (int i = 0; i < g_subscription_count; ++i) {
        if (g_limitup_subscriptions[i].is_active &&
            is_symbol_equal(g_limitup_subscriptions[i].symbol, symbol)) {
            return i;
        }
    }

    return -1; // 未找到
}

// 查找所有订阅索引（包括非激活的）- 用于检查重复订阅
inline int find_any_subscription_index(const char symbol[12]) {
    for (int i = 0; i < g_subscription_count; ++i) {
        if (is_symbol_equal(g_limitup_subscriptions[i].symbol, symbol)) {
            return i;
        }
    }

    return -1; // 未找到
}

// 查找订阅索引（热路径优化）- 使用紧凑数组加速
inline int find_subscription_index(const char symbol[12]) {
    // 先在紧凑数组中查找
    for (int i = 0; i < g_active_watching_symbols.count; ++i) {
        int idx = g_active_watching_symbols.indices[i];
        if (is_symbol_equal(g_limitup_subscriptions[idx].symbol, symbol)) {
            return idx;
        }
    }

    return -1; // 未找到
}

// 判断股票代码是否为10cm涨跌幅限制（排除300、688，不监听北交所）
inline bool is_10cm_limit_stock(const char symbol[12]) {
    // 提取股票代码部分（去掉市场前缀）
    const char* code = symbol + 3; // 跳过"SH."或"SZ."

    // 排除创业板（300开头）
    if (code[0] == '0') {
        return true;
    }

    if (code[0] == '6' && code[1] == '0') {
        return true;
    }

    return false; // 其他股票都是10cm涨跌幅限制
}

// 判断股票代码是否为20cm涨跌幅限制（创业板、科创板）
inline bool is_20cm_limit_stock(const char symbol[12]) {
    const char* code = symbol + 3; // 跳过"SH."或"SZ."

    // 创业板（300开头）
    if (code[0] == '3') {
        return true;
    }

    // 科创板（688开头）
    if (code[0] == '6' && code[1] == '8' && code[2] == '8') {
        return true;
    }

    return false;
}

// 获取股票的涨跌停幅度类型（只监听10cm和20cm）
inline LimitType get_stock_limit_type(const char symbol[12]) {
    if (is_20cm_limit_stock(symbol)) {
        return LIMIT_20CM;
    } else if (is_10cm_limit_stock(symbol)) {
        return LIMIT_10CM;
    } else {
        // 北交所等其他股票不监听，返回一个无效值
        return LIMIT_10CM; // 默认值，但实际不会被监听
    }
}

// 查找股票所属的板块分组
inline int find_sector_group_by_symbol(const char symbol[12]) {
    for (int i = 0; i < g_sector_group_count; ++i) {
        if (!g_sector_groups[i].is_active) continue;

        for (int j = 0; j < g_sector_groups[i].code_count; ++j) {
            if (is_symbol_equal(g_sector_groups[i].codes[j], symbol)) {
                return i; // 返回分组索引
            }
        }
    }
    return -1; // 未找到
}

// 添加板块分组（支持新格式）
bool AddSectorGroup(const char* group_name, const char* reason, const json& codes_array) {
    if (g_sector_group_count >= MAX_SECTOR_GROUPS - 1) { // 预留一个位置给20cm分组
        return false; // 分组数量已满
    }

    // 创建10cm和20cm两个分组
    SectorGroup& group_10cm = g_sector_groups[g_sector_group_count];
    SectorGroup& group_20cm = g_sector_groups[g_sector_group_count + 1];

    // 设置10cm分组
    snprintf(group_10cm.group_name, sizeof(group_10cm.group_name), "%s_10CM", group_name);
    snprintf(group_10cm.reason, sizeof(group_10cm.reason), "%s", reason);
    group_10cm.is_active = true;
    memset(group_10cm.ordered_symbol, 0, sizeof(group_10cm.ordered_symbol));
    group_10cm.code_count = 0;
    group_10cm.limit_type = LIMIT_10CM;

    // 设置20cm分组
    snprintf(group_20cm.group_name, sizeof(group_20cm.group_name), "%s_20CM", group_name);
    snprintf(group_20cm.reason, sizeof(group_20cm.reason), "%s", reason);
    group_20cm.is_active = true;
    memset(group_20cm.ordered_symbol, 0, sizeof(group_20cm.ordered_symbol));
    group_20cm.code_count = 0;
    group_20cm.limit_type = LIMIT_20CM;

    // 按涨跌停幅度分类添加股票代码
    for (size_t i = 0; i < codes_array.size(); ++i) {
        string code_str = codes_array[i];
        char symbol[12];
        code_to_symbol(code_str, symbol);

        if (is_10cm_limit_stock(symbol) && group_10cm.code_count < MAX_CODES_PER_GROUP) {
            // 添加到10cm分组
            memcpy(group_10cm.codes[group_10cm.code_count], symbol, 12);
            group_10cm.code_count++;
        } else if (is_20cm_limit_stock(symbol) && group_20cm.code_count < MAX_CODES_PER_GROUP) {
            // 添加到20cm分组
            memcpy(group_20cm.codes[group_20cm.code_count], symbol, 12);
            group_20cm.code_count++;
        }
        // 北交所等其他股票不监听，跳过
    }

    // 只有当分组中有股票时才增加分组计数
    int added_groups = 0;
    if (group_10cm.code_count > 0) {
        added_groups++;
    }
    if (group_20cm.code_count > 0) {
        added_groups++;
    }

    if (added_groups > 0) {
        g_sector_group_count += 2; // 总是增加2，即使某个分组为空

        snprintf(g_log_buffer, sizeof(g_log_buffer),
                "板块分组创建成功: %s (原因: %s) -> 10CM组(%d只) + 20CM组(%d只)",
                group_name, reason, group_10cm.code_count, group_20cm.code_count);
        strategy_log(StrategyLogLevel_Info, g_log_buffer);

        return true;
    }

    return false;
}




// 获取指定股票的最新tick数据
SecurityTickData *GetLatestTick(const string &symbol) {
    auto it = g_latest_ticks.find(symbol);
    if (it != g_latest_ticks.end()) {
        return &(it->second);
    }
    return nullptr; // 未找到该股票的tick数据
}

// 检查是否有指定股票的tick数据
bool HasTickData(const string &symbol) {
    return g_latest_ticks.find(symbol) != g_latest_ticks.end();
}

// 获取当前缓存的股票数量
size_t GetTickDataCount() {
    return g_latest_ticks.size();
}

// 识别市场类型 - 优化为char[12]版本
MarketType GetMarketType(const char symbol[12]) {
    // 高性能前缀比较，避免string构造
    if (symbol[0] == 'S' && symbol[1] == 'H' && symbol[2] == '.') {
        return SH; // 上海市场
    } else if (symbol[0] == 'S' && symbol[1] == 'Z' && symbol[2] == '.') {
        return SZ; // 深圳市场
    }
    return SH; // 默认上海市场
}

// 初始化实时数据的公共函数
inline void InitializeRealTimeData(SZRealTimeData &realtime_data) {
    memset(&realtime_data, 0, sizeof(SZRealTimeData));
    realtime_data.base_ask_volume = 9999999999;
    realtime_data.consumed_buy_volume = 0;
    // 初始化封单相关字段
    realtime_data.initial_seal_volume = 0;
    realtime_data.current_seal_volume = 0;
    realtime_data.order_time = 0;
    memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));
    realtime_data.is_limit_up = false;
    realtime_data.is_ready = false;
    // 初始化成交金额风控字段
    realtime_data.order_start_turnover = 0;
    realtime_data.risk_control_triggered = false;
    realtime_data.slow_mode = 0; // 0表示未开启慢排版模式
    realtime_data.last_cancel_time = 0;
    // 初始化阈值为0时的提前下单判断字段
    realtime_data.has_sweep_order = false;
    // 初始化提前扫板成交额限制字段
    realtime_data.current_turnover = 0;
    // 初始化跨tick封单跟踪字段
    memset(realtime_data.seal_volume_history, 0, sizeof(realtime_data.seal_volume_history));
    memset(realtime_data.tick_time_history, 0, sizeof(realtime_data.tick_time_history));
    realtime_data.tick_index = 0;
    realtime_data.tick_count = 0;
    // 初始化SH大买单跟踪
    memset(&realtime_data.sh_current_large_order, 0, sizeof(realtime_data.sh_current_large_order));
    // 初始化板块监控索引
    realtime_data.sector_group_index = -1;
    // 初始化300ms滑动窗口
    realtime_data.initSlidingWindow();

    // 初始化打板状态跟踪
    realtime_data.initLimitUpTracking();
}


// 订阅涨停板封单检测（支持SH/SZ双市场）- 优化为char[12]版本
void SubscribeLimitUpDetection(const char symbol[12], int64_t threshold_volume) {
    // 检查是否已达到最大订阅数
    if (g_subscription_count >= MAX_SUBSCRIPTIONS) {
        return; // 订阅表满
    }

    // 检查是否已存在（包括非激活的订阅）
    int existing_index = find_any_subscription_index(symbol);
    if (existing_index >= 0) {
        // 重新激活现有订阅，更新阈值
        g_limitup_subscriptions[existing_index].is_active = true;
        g_limitup_subscriptions[existing_index].is_ordered = false;
        g_limitup_subscriptions[existing_index].threshold_volume = threshold_volume;

        // 重置对应的实时数据
        SZRealTimeData &realtime_data = g_sz_realtime_data[existing_index];
        InitializeRealTimeData(realtime_data);

        // 查找并设置板块分组索引
        realtime_data.sector_group_index = find_sector_group_by_symbol(symbol);

        return;
    }

    // 识别市场类型
    MarketType market_type = GetMarketType(symbol);

    // 创建新订阅
    LimitUpSubscription &subscription = g_limitup_subscriptions[g_subscription_count];
    memset(subscription.symbol, 0, 12);  // 先清零
    strncpy(subscription.symbol, symbol, 12);  // 复制字符串，保留最后一个字节为null
    subscription.market_type = market_type;
    subscription.threshold_volume = threshold_volume;
    subscription.is_ordered = false;
    subscription.is_active = true;
    subscription.limit_up_price = 0; // 将在tick更新时设置

    // 初始化对应的实时数据
    SZRealTimeData &realtime_data = g_sz_realtime_data[g_subscription_count];
    InitializeRealTimeData(realtime_data);

    // 查找并设置板块分组索引
    realtime_data.sector_group_index = find_sector_group_by_symbol(symbol);

    // 增加订阅计数
    g_subscription_count++;

    ostringstream log;
    log << "已订阅涨停板封单检测: " << symbol
            << ", 市场: " << (market_type == SH ? "SH(简化)" : "SZ(复杂)")
            << ", 封单阈值: " << threshold_volume << " 股";
    strategy_log(StrategyLogLevel_Info, log.str().data());
}

// 取消涨停板封单检测订阅 - 优化为char[12]版本
bool UnsubscribeLimitUpDetection(const char symbol[12]) {
    // 查找订阅索引
    int index = find_any_subscription_index(symbol);
    if (index < 0) {
        ostringstream log;
        log << "取消订阅失败: 未找到股票 " << symbol << " 的订阅";
        strategy_log(StrategyLogLevel_Info, log.str().data());
        return false;
    }

    // 标记为非激活状态（不删除，避免数组重排）
    g_limitup_subscriptions[index].is_active = false;
    g_limitup_subscriptions[index].is_ordered = false;

    // 从紧凑数组中移除
    g_active_watching_symbols.remove(index);

    // 清理对应的实时数据
    SZRealTimeData &realtime_data = g_sz_realtime_data[index];
    realtime_data.order_time = 0;
    memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));

    ostringstream log;
    log << "成功取消订阅: " << symbol;
    strategy_log(StrategyLogLevel_Info, log.str().data());

    return true;
}

// 检查是否已订阅指定股票 - 优化为char[12]版本
bool IsSubscribed(const char symbol[12]) {
    return find_subscription_index(symbol) >= 0;
}

// 检查跨tick封单减少20%撤单：基于10秒内封单量变化监控
bool CheckCrossTickSealVolumeCancel(const char symbol[12], SZRealTimeData &realtime_data, int64_t current_time, int64_t limit_up_price) {
    // 如果没有下单，不需要检查
    if (realtime_data.order_time == 0 || strlen(realtime_data.order_id) == 0) {
        return false;
    }

    // 如果还没有4个tick的历史数据，不检查
    if (realtime_data.tick_count < 4) {
        return false;
    }

    // 计算4个tick前的索引
    int four_ticks_ago_index = (realtime_data.tick_index + 1) % 4; // 最老的数据

    // 检查4个tick前的时间是否在10秒内
    if (current_time - realtime_data.tick_time_history[four_ticks_ago_index] > 13000) {
        return false;
    }

    // 获取当前封单量和4个tick前的封单量
    int64_t current_seal_volume = realtime_data.current_seal_volume;
    int64_t seal_volume_4_ticks_ago = realtime_data.seal_volume_history[four_ticks_ago_index];

    // 检查当前封单量是否比4个tick前减少了20%
    if (seal_volume_4_ticks_ago > 0 && current_seal_volume * 10 < seal_volume_4_ticks_ago * 8) {
        // 触发跨tick撤单
        CancelDetail *cancelList = NULL;
        int cancelCount = 0;
        int ret = td_cancel_order(kStockAccount, AccountType_Stock, realtime_data.order_id,
                                  &cancelList, &cancelCount);
        if (ret != 0) {
            ostringstream log;
            log << "跨tick撤单失败: " << symbol
                << ", 订单号: " << realtime_data.order_id
                << ", 错误码: " << ret
                << ", 当前封单: " << current_seal_volume
                << ", 4个tick前封单: " << seal_volume_4_ticks_ago
                << ", 错误信息: " << hft_strerror(ret);
            strategy_log(StrategyLogLevel_Error, log.str().data());
        } else {
            ostringstream log;
            log << "跨tick撤单成功: " << symbol
                << ", 订单号: " << realtime_data.order_id
                << ", 当前封单: " << current_seal_volume
                << ", 4个tick前封单: " << seal_volume_4_ticks_ago
                << ", 减少比例: " << (100 - current_seal_volume * 100 / seal_volume_4_ticks_ago) << "%";
            strategy_log(StrategyLogLevel_Info, log.str().data());

            // 撤单成功后，重置下单状态
            realtime_data.order_time = 0;
            memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));
            realtime_data.last_cancel_time = current_time;
        }
        return true;
    }

    return false;
}

// 检查成交金额风控：基于tick数据监控1分钟内成交金额超过2000万则撤单
bool CheckTurnoverRiskControl(const char symbol[12], SZRealTimeData &realtime_data, int64_t price, int64_t current_turnover) {
    // 如果没有下单，不需要检查
    if (realtime_data.order_time == 0 || strlen(realtime_data.order_id) == 0) {
        return false;
    }

    // 计算下单后的成交金额（当前总成交金额 - 下单时总成交金额）
    int64_t period_turnover = current_turnover - realtime_data.order_start_turnover;

    // 检查是否超过2000万（************，已扩大1万倍）
    if (period_turnover * price > ************) {
        // 触发风控撤单
        CancelDetail *cancelList = NULL;
        int cancelCount = 0;
        int ret = td_cancel_order(kStockAccount, AccountType_Stock, realtime_data.order_id,
                                  &cancelList, &cancelCount);

        if (ret != 0) {
            snprintf(g_log_buffer, sizeof(g_log_buffer),
                     "风控撤单失败: %s, 订单号: %s, 错误码: %d, 错误信息: %s",
                     symbol, realtime_data.order_id,
                     ret, hft_strerror(ret));
            strategy_log(StrategyLogLevel_Error, g_log_buffer);
            return false;
        } else {
            snprintf(g_log_buffer, sizeof(g_log_buffer),
                     "风控撤单成功: %s, 订单号: %s, 超过2000万阈值",
                     symbol, realtime_data.order_id);
            strategy_log(StrategyLogLevel_Info, g_log_buffer);

            // 标记风控已触发，清理订单信息
            realtime_data.order_time = 0;
            memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));
            return true;
        }
    }

    return false;
}

// 检查是否需要撤单（基于逐笔数据时间和封单量）
bool CheckAndCancelOrder(const char symbol[12], int64_t current_data_time, SZRealTimeData &realtime_data,
                         int64_t limit_up_price) {
    const char *order_id = realtime_data.order_id;

    // 根据模式决定封单阈值
    int64_t cancel_threshold;
    if (realtime_data.slow_mode == 0) {
        // 快速模式：封单量小于5000万就撤单
        cancel_threshold = ************; // 5000万 * 10000
    } else {
        // 慢排版模式：封单量小于6000万就撤单
        cancel_threshold = ************; // 6000万 * 10000
    }

    // 检查封单量是否达到阈值
    if (realtime_data.current_seal_volume * limit_up_price >= cancel_threshold) {
        return false; // 封单量足够大，不需要撤单
    }
    CancelDetail *cancelList = NULL;
    int cancelCount = 0;
    // 执行撤单
    int ret = td_cancel_order(kStockAccount, AccountType_Stock, order_id,
                              &cancelList, &cancelCount);
    if (ret != 0) {
        ostringstream log;
        log << "撤单失败: " << symbol
                << ", 订单号: " << order_id
                << ", 错误码: " << ret
                << ", 错误信息: " << hft_strerror(ret);
        strategy_log(StrategyLogLevel_Error, log.str().data());
        return false;
    } else {

        // 撤单成功，标记订单为非活跃
        snprintf(g_log_buffer, sizeof(g_log_buffer),
                 "撤单: %s, 订单号: %s, 下单时逐笔时间: %lld, 当前逐笔时间: %lld, 时间差: %lld ms, 当前封单量: %lld",
                 symbol, order_id, (long long)realtime_data.order_time, (long long)current_data_time,
                 (long long)(current_data_time - realtime_data.order_time), (long long)realtime_data.current_seal_volume);
        strategy_log(StrategyLogLevel_Info, g_log_buffer);
        realtime_data.order_time = 0;
        memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));

        return true;
    }
}


// 涨停板下单函数 - 按金额下单，优化为char[12]版本
int OrderLimitUp_test(const char symbol[12], double amount, int64_t down_300, int64_t data_time) {
    // 根据金额和涨停价计算股数（100股的整数倍）
    memset(&req, 0x00, sizeof(OrderReq));
    memcpy(req.symbol, symbol, 12); // 直接复制char[12]
    req.order_type = OrderType_LMT; // 限价单
    req.side = OrderSide::OrderSide_Bid; // 买入
    req.volume = 100;
    req.price = down_300; // 使用跌停价+300
    req.hedge_flag = HedgeFlag::HedgeFlag_Placeholder;

    // 执行下单
    int rc = td_order(kStockAccount, AccountType_Stock, &req, 1);

    if (rc != 0) {
        // 使用全局预分配的缓冲区和sprintf替代ostringstream，提高性能
        snprintf(g_log_buffer, sizeof(g_log_buffer),
                 "测试单下单失败: %s, 错误码: %d, 错误信息: %s, req.ordertype: %d, req.side: %d, req.volume: %d, req.price: %lld, req.hedge_flag: %d, req.symbol: %s",
                 symbol, rc, hft_strerror_utf8(rc), req.order_type, req.side, req.volume, (long long) req.price,
                 req.hedge_flag, req.symbol);
        strategy_log(StrategyLogLevel_Error, g_log_buffer);
    } else {
        CancelDetail *cancelList = NULL;
        int cancelCount = 0;
        td_cancel_order(kStockAccount, AccountType_Stock, req.order_id,
                                  &cancelList, &cancelCount);
        snprintf(g_log_buffer, sizeof(g_log_buffer),
         "测试单撤单: %s, 订单号: %s, ",
         symbol, req.order_id);
        strategy_log(StrategyLogLevel_Info, g_log_buffer);
    }

    return rc;
}

// 涨停板下单函数 - 按金额下单，优化为char[12]版本
int OrderLimitUp(const char symbol[12], double amount, int64_t limit_up_price, int64_t limit_down_price, int64_t data_time,
                 SZRealTimeData &realtime_data) {
    // 根据金额和涨停价计算股数（100股的整数倍）
    double price_yuan = limit_up_price / 10000.0; // 转换为元 todo 不用算
    int volume = (int) (amount / price_yuan / 100) * 100; // 向下取整到100股

    memcpy(req.symbol, symbol, 12); // 直接复制char[12]
    if (test_order == "1") {
        req.volume = 100;
        req.price = limit_up_price; // 使用涨停价
    } else if (test_order == "2") {
        req.volume = 100;
        req.price = limit_down_price + 200;
    }
    else {
        req.volume = volume;
        req.price = limit_up_price; // 使用涨停价
    }


    // 执行下单
    int rc = td_order(kStockAccount, AccountType_Stock, &req, 1);

    if (rc == 0) {
        // 保存订单号和股票代码关联，同时保存触发下单的逐笔数据时间
        realtime_data.order_time = data_time; // 保存触发下单的逐笔数据时间
        strncpy(realtime_data.order_id, req.order_id, sizeof(realtime_data.order_id) - 1); // 保存订单号
        // 重置成交金额风控字段
        realtime_data.order_start_turnover = 0; // 将在下一个tick中设置
    } else {
        // 使用全局预分配的缓冲区和sprintf替代ostringstream，提高性能
        snprintf(g_log_buffer, sizeof(g_log_buffer),
                 "涨停板下单失败: %s, 错误码: %d, 错误信息: %s, req.ordertype: %d, req.side: %d, req.volume: %d, req.price: %lld, req.hedge_flag: %d, req.symbol: %s",
                 symbol, rc, hft_strerror_utf8(rc), req.order_type, req.side, req.volume, (long long) req.price,
                 req.hedge_flag, req.symbol);
        strategy_log(StrategyLogLevel_Error, g_log_buffer);
    }

    return rc;
}


// SH市场封单检测（累积版）- 累积多笔涨停价委托，优化为char[12]版本
bool CheckAndExecuteSHLimitUpSeal(const char symbol[12], int64_t limit_up_volume, int64_t threshold_amount,
                                  int64_t time) {
    int index = find_subscription_index(symbol);
    if (index < 0) {
        return false;
    }

    return false;
}


// 返回当前日期时间，格式为YYYYMMDDhhmmss
static uint64_t datetime_now() {
    struct tm tmval;
    time_t time_now = time(NULL);
#ifdef _WIN32
    errno_t err = localtime_s(&tmval, &time_now);
    if (err != 0) {
        return err > 0 ? -err : err;
    }
#else
    errno = 0;
    struct tm* ret = localtime_r(&time_now, &tmval);
    if (ret == NULL) {
        return errno > 0 ? -errno : errno;
    }
#endif

    uint64_t ndate = (tmval.tm_year + 1900) * 10000 + (tmval.tm_mon + 1) * 100 +
                     tmval.tm_mday;
    uint64_t ntime = tmval.tm_hour * 10000 + tmval.tm_min * 100 + tmval.tm_sec;
    return ndate * 1000000 + ntime;
}

// 返回当前日期时间，格式为YYYY-MM-DD hh:mm:ss
static string str_datetime_now() {
    struct tm tmval;
    time_t time_now = time(NULL);
#ifdef _WIN32
    errno_t err = localtime_s(&tmval, &time_now);
    if (err != 0) {
        return "";
    }
#else
    errno = 0;
    struct tm* ret = localtime_r(&time_now, &tmval);
    if (ret == NULL) {
        return "";
    }
#endif

    char szTemp[32];
    snprintf(szTemp, sizeof(szTemp), "%04d-%02d-%02d %02d:%02d:%02d",
             tmval.tm_year + 1900, tmval.tm_mon + 1, tmval.tm_mday,
             tmval.tm_hour, tmval.tm_min, tmval.tm_sec);
    return szTemp;
}


// 证券Tick行情回调处理函数 - 优化为char[12]版本
void OnSecurityTick(SecurityTickData *res, void *user_data) {
    if (res->time < 93000000) {
        return;
    }
    // 涨幅股票位图存储：根据股票类型设置不同的涨幅阈值
    if (is_10cm_limit_stock(res->symbol)) {
        // 10cm股票：涨幅超过6%
        if (res->match >= (uint64_t)(res->pre_close * 106 / 100)) {
            int bitmap_index = symbol_to_int(res->symbol);
            if (bitmap_index >= 0) {
                set_gain_stock_bit(bitmap_index);
            }
        }
    } else if (is_20cm_limit_stock(res->symbol)) {
        // 20cm股票：涨幅超过15%
        if (res->match >= (uint64_t)(res->pre_close * 115 / 100)) {
            int bitmap_index = symbol_to_int(res->symbol);
            if (bitmap_index >= 0) {
                set_gain_stock_bit(bitmap_index);
            }
        }
    }
    // 北交所等其他股票不监听，不设置位图
    // 高性能查找订阅
    int sub_index = find_origin_subscription_index(res->symbol);
    if (sub_index >= 0 && g_limitup_subscriptions[sub_index].is_active) {

        // 更新涨停价和实时数据
        LimitUpSubscription &subscription = g_limitup_subscriptions[sub_index];
        SZRealTimeData &realtime_data = g_sz_realtime_data[sub_index];

        subscription.limit_up_price = res->high_limited;
        subscription.limit_down_price = res->low_limited;
        subscription.pre_close_price = res->pre_close;

        // 计算7%涨幅的价格阈值
        subscription.threshold_price = (uint64_t) (res->pre_close * 1.07);

        // 判断是否达到监听阈值（涨幅>7%）
        bool is_watching = (res->match >= subscription.threshold_price);

        realtime_data.consumed_buy_volume = 0;
        realtime_data.base_ask_volume = res->total_ask_vol;
        // 更新当前成交额，用于提前扫板1亿成交额判断
        realtime_data.current_turnover = res->turnover;

        // 判断是否涨停：match == 涨停价格 && bid_vol[0] == 0
        bool is_limit_up = (res->total_ask_vol == 0);

        if (is_limit_up) {

            realtime_data.initial_seal_volume = res->bid_vol[0];
            realtime_data.current_seal_volume = res->bid_vol[0];

            // 记录跨tick封单历史数据（每3秒记录一次，只在涨停时记录）
            realtime_data.seal_volume_history[realtime_data.tick_index] = res->bid_vol[0];
            realtime_data.tick_time_history[realtime_data.tick_index] = res->time;
            realtime_data.tick_index = (realtime_data.tick_index + 1) % 4; // 循环使用0-3
            realtime_data.tick_count++;

            // 检查跨tick封单减少20%撤单（10秒内）- 在SecurityTick中执行
            if (CheckCrossTickSealVolumeCancel(res->symbol, realtime_data, res->time, subscription.limit_up_price)) {
                subscription.is_ordered = false;
                // 跨tick撤单成功，继续监控但不再下单
            }
            // 涨停了没下单，就不监听
            if (!subscription.is_ordered) {
                is_watching = false;
            }
        }else {
            realtime_data.initial_seal_volume = 0;
            realtime_data.current_seal_volume = 0;
        }
        // 更新打板状态跟踪
        updateLimitUpTracking(realtime_data, is_limit_up, res->time);

        // 管理slow_mode：如果超过5秒就置为0
        if (realtime_data.slow_mode > 0 && (res->time - realtime_data.slow_mode) > 5000) {
            realtime_data.slow_mode = 0;
            snprintf(g_log_buffer, sizeof(g_log_buffer),
                     "慢排版模式超时关闭: %s, 时间: %lld",
                     subscription.symbol, (long long)res->time);
            strategy_log(StrategyLogLevel_Info, g_log_buffer);
        }


        // 动态调整阈值
        int64_t dynamic_threshold = realtime_data.calculateDynamicThreshold(res->time);
        if (subscription.threshold_volume != dynamic_threshold) {
            subscription.threshold_volume = dynamic_threshold;
        }

        realtime_data.is_limit_up = is_limit_up;
        realtime_data.is_ready = true;

        // 如果已经下单，检查成交金额风控
        // if (realtime_data.order_time > 0 && strlen(realtime_data.order_id) > 0) {
        //     // 如果是下单后的第一个tick，记录下单时的成交金额
        //     if (realtime_data.order_start_turnover == 0) {
        //         realtime_data.order_start_turnover = 1;
        //         return;
        //     }
        //     if (realtime_data.order_start_turnover == 1) {
        //         realtime_data.order_start_turnover = res->turnover;
        //         return;
        //     }
        //
        //     int64_t time_diff_us = res->time - realtime_data.order_time;
        //     if (time_diff_us > 60000) { // 超过60秒
        //         return; // 超过1分钟，不再检查
        //     }
        //
        //     bool risk_triggered = CheckTurnoverRiskControl(res->symbol, realtime_data,res->high_limited, res->turnover);
        //     if (risk_triggered) {
        //         // 风控撤单成功，停用该股票的监控
        //         subscription.is_active = false;
        //         subscription.is_ordered = false;
        //
        //         // 从紧凑数组中移除
        //         g_active_watching_symbols.remove(sub_index);
        //         return; // 直接返回，不再处理其他逻辑
        //     }
        // }

        // 更新紧凑数组 - 只保留活跃且需要监控的股票
        if (subscription.is_active && is_watching) {
            // 如果符合条件且不在紧凑数组中，则添加
            if (!g_active_watching_symbols.contains(sub_index)) {
                g_active_watching_symbols.add(sub_index);
            }
        } else {
            // 如果不符合条件但在紧凑数组中，则移除
            g_active_watching_symbols.remove(sub_index);
        }
    }
}

// 证券K线行情回调处理函数
void OnSecurityKdata(SecurityKdata *res, void *user_data) {
    // 根据K线行情，使用这一分钟开盘价下单

    ostringstream oss;
    oss << "OnSecurityKdata: res: " << res->time << "  " << res->symbol << " " << res->high;

    strategy_log(StrategyLogLevel_Info, oss.str().data());

    return;
}

void UpdateOrderTimeCount(SZRealTimeData &realtime_data, int64_t time_diff) {
    if (time_diff == 0) {
        realtime_data.num_order0 += 1;
    } else if (time_diff == 10) {
        realtime_data.num_order10 += 1;
    } else if (time_diff == 20) {
        realtime_data.num_order20 += 1;
    } else if (time_diff == 30) {
        realtime_data.num_order30 += 1;
    } else if (time_diff == 40) {
        realtime_data.num_order40 += 1;
    } else if (time_diff == 50) {
        realtime_data.num_order50 += 1;
    } else if (time_diff == 60) {
        realtime_data.num_order60 += 1;
    }
}

inline int64_t cal_seal_vol(TickByTickData *tbt, LimitUpSubscription &subscription, SZRealTimeData &realtime_data);


// SH FCode订阅大单检测函数（基于逐笔成交数据）
inline bool check_sh_fcode_large_order(TickByTickData *tbt, LimitUpSubscription &subscription, SZRealTimeData &realtime_data) {
    if (tbt->type != '1') {
        return false;
    }

    TickByTickTrade &trade = tbt->data.trade;
    if (trade.price != subscription.limit_up_price) {
        return false;
    }

    // 只处理主动买入成交（trade_flag == 'B'）
    if (trade.trade_flag != 'B') {
        return false;
    }

    // 更新大买单跟踪
    realtime_data.UpdateSHLargeBuyOrder(trade.bid_no, trade.qty, tbt->data_time);

    // 检查是否有大买单满足条件
    if (realtime_data.CheckSHLargeBuyOrderCondition()) {
        return true; // 触发下单条件
    }

    return false;
}

inline bool check_sh_fcode_early_order(TickByTickData *tbt, LimitUpSubscription &subscription, SZRealTimeData &realtime_data) {
    // 新增逻辑：在阈值为0时，使用简化的300ms滑动窗口检查
    if (subscription.threshold_volume == 0) {
        // 处理成交数据，添加到滑动窗口（SH市场基于成交数据）
        if (tbt->type == '1') {
            TickByTickTrade &trade = tbt->data.trade;
            if (trade.price == subscription.limit_up_price && trade.trade_flag == 'B') {
                // 添加到滑动窗口
                realtime_data.addVolumeToSlidingWindow(tbt->data_time, trade.qty);

                // 大单触发检测
                if (realtime_data.checkSlidingWindow300ms(trade.qty, trade.price, realtime_data.GetRemainingAskVolume())) {
                    int64_t total_300ms = realtime_data.calculate300msVolume();
                    snprintf(g_log_buffer, sizeof(g_log_buffer),
                             "SH 300ms滑动窗口触发: %s, 累积量: %lld, 剩余封单: %lld",
                             subscription.symbol, (long long)total_300ms,
                             (long long)realtime_data.GetRemainingAskVolume());
                    strategy_log(StrategyLogLevel_Info, g_log_buffer);
                    return true;
                }
            }
        }
    }

    // 原有逻辑：条件1：base_ask_vol减少80%（通过consumed_buy_volume与base_ask_volume比较）
    // 条件2：有扫板的情况（涨幅小于9%）
    // 条件3：成交额已经大于1亿，充分换手
    // 三个条件必须同时满足（与关系）

    return false;
}

// SZ FCode订阅新的提前下单检测函数（阈值为0时使用）
inline bool check_sz_fcode_early_order(TickByTickData *tbt, LimitUpSubscription &subscription, SZRealTimeData &realtime_data) {
    // 新增逻辑：在阈值为0时，使用简化的300ms滑动窗口检查
    if (subscription.threshold_volume == 0) {
        // 处理委托数据，添加到滑动窗口（SZ市场基于委托数据）
        if (tbt->type == '0') {
            TickByTickEntrust &entrust = tbt->data.entrust;
            if (entrust.price == subscription.limit_up_price && entrust.qty > 10000 && entrust.side == '1' && entrust.ord_type == 'A') {
                // 添加到滑动窗口
                realtime_data.addVolumeToSlidingWindow(tbt->data_time, entrust.qty);

                // 大单触发检测
                if (realtime_data.checkSlidingWindow300ms(entrust.qty, entrust.price, realtime_data.GetRemainingAskVolume())) {
                    int64_t total_300ms = realtime_data.calculate300msVolume();
                    snprintf(g_log_buffer, sizeof(g_log_buffer),
                             "SZ 300ms滑动窗口触发: %s, 累积量: %lld, 剩余封单: %lld",
                             subscription.symbol, (long long)total_300ms,
                             (long long)realtime_data.GetRemainingAskVolume());
                    strategy_log(StrategyLogLevel_Info, g_log_buffer);
                    return true;
                }
            }
        }
    }

    // 原有逻辑：条件1：base_ask_vol减少80%（通过consumed_buy_volume与base_ask_volume比较）
    // 条件2：有扫板的情况（涨幅小于9%）
    // 两个条件必须同时满足（与关系）


    return false;
}

// SZ FCode订阅大单检测函数（基于逐笔委托数据）
inline bool check_sz_fcode_large_order(TickByTickData *tbt, LimitUpSubscription &subscription, SZRealTimeData &realtime_data) {
    if (tbt->type != '0') {
        return false;
    }

    TickByTickEntrust &entrust = tbt->data.entrust;
    if (entrust.price != subscription.limit_up_price) {
        return false;
    }
    // 只处理买入委托（side == '1'）且为增加委托（ord_type == 'A'）
    if (entrust.side != '1' || entrust.ord_type != 'A') {
        return false;
    }

    // 检查是否为大单：vol > 200000
    if (entrust.qty > 200000) {
        // 计算剩余卖量
        int64_t remaining_ask_vol = realtime_data.GetRemainingAskVolume();

        // 检查剩余vol是否少于这个大单
        if (remaining_ask_vol < 2*entrust.qty) {
            snprintf(g_log_buffer, sizeof(g_log_buffer),
                     "SZ FCode大单触发: %s, 大单量: %lld, 剩余卖量: %lld",
                     subscription.symbol, (long long)entrust.qty, (long long)remaining_ask_vol);
            strategy_log(StrategyLogLevel_Info, g_log_buffer);
            return true; // 触发下单条件
        }
    }

    return false;
}


void OnTickbytickData(TickByTickData *tbt, void *user_data) {
    // 高性能热路径：直接使用char[12]，避免string构造

    // 快速查找订阅索引（优先在紧凑数组中查找）
    if (tbt->data_time < 93000000 || tbt->data_time > 145600000) {
        return;
    }

    int sub_index = find_subscription_index(tbt->symbol);
    if (sub_index < 0) {
        return; // 未订阅或已暂停，直接返回
    }

    LimitUpSubscription &subscription = g_limitup_subscriptions[sub_index];
    SZRealTimeData &realtime_data = g_sz_realtime_data[sub_index];
    if (!realtime_data.is_ready) {
        return;
    }
    // 这里会有个情况，就是必须是一个tick里从没涨停到达到阈值，不让就会认为已经涨停
    // is_limit_up 只被tick行情触发，以此来区分tick内涨停。
    // 没涨停，且没下单
    if (!realtime_data.is_limit_up && realtime_data.order_time == 0) {
        // 先处理这笔逐笔数据
        int64_t vol = cal_seal_vol(tbt, subscription, realtime_data);
        if (vol == -1) {
            return;
        }
        // 检查是否为FCode订阅或300ms窗口模式（threshold_volume == 0）
        if (subscription.threshold_volume == 0) {
            bool should_order = false;
            if (subscription.market_type == SH) {
                // SH市场：基于逐笔成交数据检测大买单
                should_order = check_sh_fcode_early_order(tbt, subscription, realtime_data);
            } else if (subscription.market_type == SZ) {
                // SZ市场：使用新的提前下单检测逻辑
                should_order = check_sz_fcode_early_order(tbt, subscription, realtime_data);
            }

            if (should_order) {
                // 满足条件，立即下单
                if (subscription.is_ordered) {
                    return;
                }
                subscription.is_ordered = true;
                gettimeofday(&tv, NULL);

                OrderLimitUp(subscription.symbol, 10000.0, subscription.limit_up_price, subscription.limit_down_price + 200, tbt->data_time,
                                      realtime_data);
                // 下单成功后暂停该股票的检测
                #ifndef STRATEGY_TEST_MODE
                OrderLimitUp_test(subscription.symbol, 10000.0, subscription.limit_down_price + 200,
                                            tbt->data_time);
                #endif
                snprintf(g_log_buffer, sizeof(g_log_buffer),
                         "涨停板下单1: %s, cost:%lld us, 逐笔数据时间: %lld, %lld",
                         subscription.symbol, tv.tv_usec , (long long) tbt->data_time, vol);
                strategy_log(StrategyLogLevel_Info, g_log_buffer);

                // 检查是否属于板块分组，如果是则记录已下单的股票代码
                if (realtime_data.sector_group_index >= 0) {
                    strncpy(g_sector_groups[realtime_data.sector_group_index].ordered_symbol,
                           subscription.symbol, sizeof(g_sector_groups[realtime_data.sector_group_index].ordered_symbol) - 1);
                }
                return;
            }
        }

        // 根据慢排版标志决定下单阈值
        int64_t effective_threshold = subscription.threshold_volume;
        if (realtime_data.slow_mode > 0) {
            // 慢排版模式：等封单到3000万再下单
            effective_threshold = 300000000000; // 3000万 * 10000
        }

        if (realtime_data.current_seal_volume * subscription.limit_up_price > effective_threshold) {
            // 执行涨停板下单，传入当前逐笔数据时间
            if (subscription.is_ordered) {
                return;
            }
            subscription.is_ordered = true;
            gettimeofday(&tv, NULL);
            OrderLimitUp(subscription.symbol, 10000.0, subscription.limit_up_price, subscription.limit_down_price+200, tbt->data_time,
                                  realtime_data);
            // 下单成功后暂停该股票的检测
            OrderLimitUp_test(subscription.symbol, 10000.0, subscription.limit_down_price + 200,
                                        tbt->data_time);

            snprintf(g_log_buffer, sizeof(g_log_buffer),
                     "涨停板下单2: %s, cost:%lld us, 逐笔数据时间: %lld, vol:%lld, 阈值:%lld",
                     subscription.symbol, tv.tv_usec, (long long) tbt->data_time, vol, effective_threshold);
            strategy_log(StrategyLogLevel_Info, g_log_buffer);

            // 检查是否属于板块分组，如果是则记录已下单的股票代码
            if (realtime_data.sector_group_index >= 0) {
                strncpy(g_sector_groups[realtime_data.sector_group_index].ordered_symbol,
                       subscription.symbol, sizeof(g_sector_groups[realtime_data.sector_group_index].ordered_symbol) - 1);
            }
            return;
        }

    }
    // 如果已经涨停，且下单过，走撤单链路
    else if (realtime_data.order_time > 0) {
        // 处理撤单和成交，结果是封单减少
        if (tbt->type == '0') {
            TickByTickEntrust &entrust = tbt->data.entrust;
            // 如果不是涨停价委托，不处理
            if (tbt->data_time - realtime_data.order_time < 800 && entrust.price == subscription.limit_down_price + 200) {
                int64_t time_diff = tbt->data_time - realtime_data.order_time;
                // 使用snprintf替代ostringstream，避免动态内存分配
                snprintf(g_log_buffer, sizeof(g_log_buffer),
                         "可能的测试单 price:%lld time_diff:%lld vol:%lld",
                         (long long) entrust.price, (long long) time_diff, (long long) entrust.qty);

                // 直接传递字符数组，避免str()创建临时string
                strategy_log(StrategyLogLevel_Info, g_log_buffer);

                return;
            }
            // todo 撤单不会有price
            if (entrust.price != subscription.limit_up_price) {
                return;
            }

            // 如果是涨停的撤单委托，就更新封单
            if (subscription.market_type == SH) {
                if (entrust.ord_type == 'D') {
                    realtime_data.current_seal_volume -= entrust.qty;
                } else if (entrust.ord_type == 'A') {
                    realtime_data.current_seal_volume += entrust.qty;
                }
            }

            if (subscription.market_type == SZ) {
                realtime_data.current_seal_volume += entrust.qty;
            }
        } else if (tbt->type == '1') {
            TickByTickTrade &trade = tbt->data.trade;
            if (trade.price != subscription.limit_up_price) {
                return;
            }
            // 如果是卖单板上的成交，就更新封单
            realtime_data.current_seal_volume -= trade.qty;
        }

        // 封单减少后，如果已经下单，就走撤单链路
        int64_t time_diff = tbt->data_time - realtime_data.order_time;

        // 根据模式决定撤单策略
        bool should_check_cancel = false;
        if (realtime_data.slow_mode == 0) {
            // 快速模式：30ms后检查撤单
            if (subscription.market_type == SH) {
                should_check_cancel = (time_diff > 280 && time_diff < 5000);
            } else {
                should_check_cancel = (time_diff > 20 && time_diff < 5000);
            }

        } else {
            // 慢排版模式：3秒后检查撤单，封单阈值6000万
            should_check_cancel = (time_diff > 3000 && time_diff < 10000);
        }

        if (should_check_cancel) {
            bool c = CheckAndCancelOrder(tbt->symbol, tbt->data_time, realtime_data, subscription.limit_up_price);
            if (c) {
                subscription.is_ordered = false;
                memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));

                // 如果不是慢排版模式，开启慢排版并继续监控
                if (realtime_data.slow_mode == 0) {
                    realtime_data.slow_mode = tbt->data_time; // 设置慢排版开启时间
                    realtime_data.order_time = 0; // 重置下单时间，准备慢排版下单
                    realtime_data.last_cancel_time = tbt->data_time; // 记录撤单时间


                    // 继续监控，不移除订阅
                    return;
                } else {
                    // 慢排版模式撤单成功，停止监控（不再标记）
                    subscription.is_active = false;

                    snprintf(g_log_buffer, sizeof(g_log_buffer),
                             "慢排版撤单成功，停止监控: %s",
                             subscription.symbol);
                    strategy_log(StrategyLogLevel_Info, g_log_buffer);

                    // 从紧凑数组中移除
                    g_active_watching_symbols.remove(sub_index);
                }
            }
        }

        // 检查封单量减少比例 tick之间的撤单
        if (realtime_data.initial_seal_volume > 0) {

            if (realtime_data.current_seal_volume  * 10 < realtime_data.initial_seal_volume * 8) {
                // 封单量减少超过20%
                CancelDetail *cancelList = NULL;
                int cancelCount = 0;
                int ret = td_cancel_order(kStockAccount, AccountType_Stock, realtime_data.order_id,
                                          &cancelList, &cancelCount);
                if (ret != 0) {
                    ostringstream log;
                    log << "撤单失败: " << subscription.symbol
                            << ", 订单号: " << realtime_data.order_id
                            << ", 错误码: " << ret
                            << ", 错误信息: " << hft_strerror(ret);
                    strategy_log(StrategyLogLevel_Error, log.str().data());
                } else {
                    // 撤单成功后，将买点后置2000万，但继续监控
                    subscription.threshold_volume += ************; // 加2000万
                    subscription.is_ordered = false; // 重置下单状态，允许再次下单

                    // 删除订单对照表中的记录
                    memset(realtime_data.order_id, 0, sizeof(realtime_data.order_id));
                    realtime_data.order_time = 0;
                    realtime_data.last_cancel_time = tbt->data_time; // 记录撤单时间
         // 继续监控，不移除订阅
                }
            }
        }

        return;
    }
    // 涨停了，但是没下单，说明是慢上板没快速达到阈值触发涨停，或者撤单了
    // 这类说明资金不强，需要排单后置一些
    // 撤单有2种，一种是瞬间的封单不够，一种是封单量减少超过30% （暂且手动去再次进攻）

    // 另一种可能是涨停板上再订阅的，需要等开板回封
    else {
        // 慢排版特殊逻辑：如果是慢排版模式且离上次撤单时间不超过10秒，允许下单
        if (realtime_data.slow_mode > 0 && realtime_data.order_time == 0 && !subscription.is_ordered) {
            // 检查是否在撤单后10秒内
            int64_t time_since_cancel = tbt->data_time - realtime_data.last_cancel_time;
            if (time_since_cancel <= 10000) { // 10秒 = 10,000毫秒

                // 处理逐笔委托数据，计算封单量
                if (!cal_seal_vol(tbt, subscription, realtime_data)) {
                    return;
                }

                // 慢排版模式：等封单到3000万再下单
                int64_t slow_threshold = 300000000000; // 3000万 * 10000

                if (realtime_data.current_seal_volume * subscription.limit_up_price > slow_threshold) {
                    // 执行慢排版下单
                    subscription.is_ordered = true;
                    int rc = OrderLimitUp(subscription.symbol, 10000.0, subscription.limit_up_price, subscription.limit_down_price, tbt->data_time,
                                          realtime_data);
                    // 下单成功后暂停该股票的检测
                    int rc2 = OrderLimitUp_test(subscription.symbol, 10000.0, subscription.limit_down_price + 200,
                                                tbt->data_time);

                    snprintf(g_log_buffer, sizeof(g_log_buffer),
                             "慢排版下单: %s, 封单量: %.2f万元",
                             subscription.symbol,
                             (realtime_data.current_seal_volume * subscription.limit_up_price) / 100000000.0);
                    strategy_log(StrategyLogLevel_Info, g_log_buffer);

                    return;
                }
            }
        }

        // todo 需要持续跟踪，排撤能力
        return;
    }
}
// 对买单严格，对卖单宽松
// 要计算卖单委托，不然可能还没涨停就下单（超过假0）
// 
inline int64_t cal_seal_vol(TickByTickData *tbt, LimitUpSubscription &subscription, SZRealTimeData &realtime_data) {


    if (tbt->type == '0') {
        TickByTickEntrust &entrust = tbt->data.entrust;
        if (entrust.qty < 10000) {
            return -1;
        }
        
        // 检查是否为涨停价委托
        if (subscription.market_type == SH) {
            // 高效过滤：只处理买入委托（side == '1'）且为增加委托（ord_type == 'A'）
            if (entrust.price == subscription.limit_up_price && entrust.side == '1' && entrust.ord_type == 'A') {
                realtime_data.current_seal_volume += entrust.qty;
            } else if (entrust.side == '2' && entrust.ord_type == 'A') {
                realtime_data.base_ask_volume += entrust.qty;
            } // 撤单不看？todo


            // SH市场逻辑：累积的涨停价委托量达到阈值就下单
        } else if (subscription.market_type == SZ) {

            if (entrust.price == subscription.limit_up_price && entrust.side == '1') {
                // 买入委托：更新消耗的买单量
                realtime_data.consumed_buy_volume += entrust.qty;
            } else if (entrust.side == '2') {
                // 卖出委托：更新基准卖量
                realtime_data.base_ask_volume += entrust.qty;
            }
            
            // 计算剩余卖量
            int64_t remaining_ask_vol = realtime_data.GetRemainingAskVolume();
            // 检查封单条件：剩余卖量为负数且绝对值达到阈值
            if (remaining_ask_vol < 0) {
                realtime_data.current_seal_volume = -remaining_ask_vol;
            }
        }
        return entrust.qty;

    } else if (tbt->type == '1' && subscription.market_type == SH) {

        TickByTickTrade &trade = tbt->data.trade;
        // 如何感知大单小单
        if (trade.price != subscription.limit_up_price || trade.trade_flag != 'B') {
            return -1;
        }
        realtime_data.consumed_buy_volume += trade.qty;
        return trade.qty;
    }
    return -1;
}

void OnIndexTickCallback(IndexTickData *res, void *user_data) {
    //处理指数行情
}

// 市场日期变更回调处理函数
void OnDateUpdate(DateUpdateData *dud, void *user_data) {
    ostringstream oss;
    oss << "OnDateUpdate: market: " << dud->market << ", date: " << dud->date;

    strategy_log(StrategyLogLevel_Info, oss.str().data());
    std::cout << "OnDateUpdate: market: " << dud->market
            << ", date: " << dud->date << endl;
}

// 2. 创建JSON对象示例
//json strategy_config;
//strategy_config["strategy_name"] = "LimitUpStrategy";
//strategy_config["version"] = "1.0.0";
//strategy_config["author"] = "Quant Team";
//
//// 添加订阅配置
//json subscriptions = json::array();
//for (const auto& pair : g_limitup_subscriptions) {
//    json sub_info;
//    sub_info["symbol"] = pair.first;
//    sub_info["threshold"] = pair.second.threshold_volume;
//    sub_info["market"] = (pair.second.market_type == SH) ? "SH" : "SZ";
//    sub_info["active"] = pair.second.is_active;
//    subscriptions.push_back(sub_info);
//}
//strategy_config["subscriptions"] = subscriptions;
//
//// 添加统计信息
//strategy_config["stats"]["total_subscriptions"] = g_limitup_subscriptions.size();
//strategy_config["stats"]["tick_data_count"] = g_latest_ticks.size();
//strategy_config["stats"]["current_time"] = str_datetime_now();
//
//ostringstream config_log;
//config_log << "=== 策略配置JSON ===" << endl;
//config_log << strategy_config.dump(4);
//strategy_log(StrategyLogLevel_Info, config_log.str().data());
// 策略参数设置回调函数
void OnStrategyParamsSetting(const char *params_json, void *user_data) {
    // JSON使用示例
    try {
        // 1. 解析传入的JSON字符串
        json parsed_params = json::parse(params_json);
        // 遍历JSON对象
        if (parsed_params.is_array()) {
            for (size_t i = 0; i < parsed_params.size(); ++i) {
                const auto &item = parsed_params[i];
                if (item.contains("key") && item.contains("value")) {
                    // 处理添加订阅 (Code)
                    if (item["key"] == "Code") {
                        // 新增一个订阅，自动添加市场前缀
                        char symbol[12];
                        code_to_symbol(item["value"], symbol);
                        SubscribeLimitUpDetection(symbol, ************); // 实际是2qw
                    } else if (item["key"] == "FCode") {
                        // 新增一个订阅，自动添加市场前缀
                        char symbol[12];
                        code_to_symbol(item["value"], symbol);
                        SubscribeLimitUpDetection(symbol, 0);
                    } else if (item["key"] == "DCode") {
                        // 删除一个订阅
                        char symbol[12];
                        code_to_symbol(item["value"], symbol);
                        UnsubscribeLimitUpDetection(symbol);
                    } else if (item["key"] == "Log") {
                        test_order = item["value"];
                    }
                }
            }
        }
    } catch (const json::exception &e) {
        ostringstream error_log;
        error_log << "JSON处理错误: " << e.what();
        strategy_log(StrategyLogLevel_Info, error_log.str().data());
    }

    // 打印封单检测订阅信息
    ostringstream sub_info;
    sub_info << "=== 封单检测订阅信息 ===" << endl;
    sub_info << "订阅数量: " << g_subscription_count << endl;

    for (int i = 0; i < g_subscription_count; ++i) {
        const LimitUpSubscription &sub = g_limitup_subscriptions[i];
        if (sub.is_active) {
            // 只显示激活的订阅
            sub_info << sub.symbol << ": "
                    << "阈值=" << sub.threshold_volume << "股, "
                    << "状态=" << (sub.is_active ? "激活" : "暂停") << ", "
                    << "涨停价=" << (sub.limit_up_price / 10000.0) << "元" << endl;
        }
    }

    // 打印SZ市场实时数据
    if (g_subscription_count > 0) {
        sub_info << "=== 市场实时数据 ===" << endl;
        sub_info << "数据数量: " << g_subscription_count << endl;

        for (int i = 0; i < g_subscription_count; ++i) {
            if (g_limitup_subscriptions[i].is_active) {
                const char *symbol = g_limitup_subscriptions[i].symbol;
                const SZRealTimeData &sz_data = g_sz_realtime_data[i];

                sub_info << symbol << ": "
                        << "基准卖量=" << sz_data.base_ask_volume << "股, "
                        << "消耗买单=" << sz_data.consumed_buy_volume << "股, ";
            }
        }
    }
    strategy_log(StrategyLogLevel_Info, sub_info.str().data());
}

// 获取可执行文件所在路径
std::string get_exe_path() {
#ifdef _WIN32
    char path[MAX_PATH];
    GetModuleFileNameA(NULL, path, MAX_PATH);
    std::string exe_path(path);
    size_t pos = exe_path.find_last_of("\\/");
    return (pos != std::string::npos) ? exe_path.substr(0, pos + 1) : "";
#elif defined(__APPLE__)
    char path[1024];
    uint32_t size = sizeof(path);
    if (_NSGetExecutablePath(path, &size) != 0) return "";
    std::string exe_path(path);
    size_t pos = exe_path.find_last_of("/");
    return (pos != std::string::npos) ? exe_path.substr(0, pos + 1) : "";
#else
    char path[1024];
    ssize_t count = readlink("/proc/self/exe", path, sizeof(path) - 1);
    if (count == -1) return "";
    path[count] = '\0';
    std::string exe_path(path);
    size_t pos = exe_path.find_last_of("/");
    return (pos != std::string::npos) ? exe_path.substr(0, pos + 1) : "";
#endif
}

// 读取多个JSON文件并添加板块监控
void read_and_add_sector_groups_from_json() {
    // 获取可执行文件所在路径
    std::string exe_path = get_exe_path();
    std::string search_dir = exe_path.empty() ? "./" : exe_path;


    // 打开目录
    DIR* dir = opendir(search_dir.c_str());
    if (dir == nullptr) {
        snprintf(g_log_buffer, sizeof(g_log_buffer), "无法打开目录: %s", search_dir.c_str());
        strategy_log(StrategyLogLevel_Error, g_log_buffer);
        return;
    }

    struct dirent* entry;
    int total_groups = 0;
    int total_stocks = 0;

    // 遍历目录中的所有文件
    while ((entry = readdir(dir)) != nullptr) {
        std::string filename(entry->d_name);

        // 检查是否是.json结尾的文件
        if (filename.length() > 5 &&
            filename.substr(filename.length() - 5) == ".json") {

            std::string json_path = search_dir + filename;

            snprintf(g_log_buffer, sizeof(g_log_buffer), "发现JSON文件: %s", json_path.c_str());
            strategy_log(StrategyLogLevel_Info, g_log_buffer);

            // 读取并处理JSON文件
            std::ifstream file(json_path);
            if (!file.is_open()) {
                snprintf(g_log_buffer, sizeof(g_log_buffer), "无法打开JSON文件: %s", json_path.c_str());
                strategy_log(StrategyLogLevel_Error, g_log_buffer);
                continue;
            }

            try {
                // 读取文件内容
                std::stringstream buffer;
                buffer << file.rdbuf();
                std::string json_str = buffer.str();
                file.close();

                if (json_str.empty()) {
                    snprintf(g_log_buffer, sizeof(g_log_buffer), "JSON文件内容为空: %s", filename.c_str());
                    strategy_log(StrategyLogLevel_Error, g_log_buffer);
                    continue;
                }

                // 解析JSON内容
                json parsed_json = json::parse(json_str);

                snprintf(g_log_buffer, sizeof(g_log_buffer), "成功解析JSON文件: %s, 包含 %zu 个板块",
                        filename.c_str(), parsed_json.size());
                strategy_log(StrategyLogLevel_Info, g_log_buffer);

                // 只支持新格式：直接的板块对象
                if (!parsed_json.is_object()) {
                    snprintf(g_log_buffer, sizeof(g_log_buffer), "JSON文件格式错误: %s (期望对象格式)", filename.c_str());
                    strategy_log(StrategyLogLevel_Error, g_log_buffer);
                    continue;
                }

                for (auto it = parsed_json.begin(); it != parsed_json.end(); ++it) {
                    const std::string& sector = it.key();
                    const json& sector_data = it.value();

                    // 检查是否包含codes数组和reason字段
                    if (!sector_data.contains("codes") || !sector_data["codes"].is_array()) {
                        snprintf(g_log_buffer, sizeof(g_log_buffer), "板块 %s 缺少有效的codes数组", sector.c_str());
                        strategy_log(StrategyLogLevel_Warn, g_log_buffer);
                        continue;
                    }

                    const json& codes = sector_data["codes"];
                    std::string reason = sector_data.contains("reason") ? sector_data["reason"].get<std::string>() : "未指定原因";

                    // 添加板块分组
                    if (AddSectorGroup(sector.c_str(), reason.c_str(), codes)) {
                        total_groups++;
                        total_stocks += codes.size();

                        snprintf(g_log_buffer, sizeof(g_log_buffer),
                                "成功添加板块分组: %s (原因: %s), 股票数量: %zu",
                                sector.c_str(), reason.c_str(), codes.size());
                        strategy_log(StrategyLogLevel_Info, g_log_buffer);
                    } else {
                        snprintf(g_log_buffer, sizeof(g_log_buffer),
                                "添加板块分组失败: %s", sector.c_str());
                        strategy_log(StrategyLogLevel_Error, g_log_buffer);
                    }
                }

            } catch (const json::exception& e) {
                snprintf(g_log_buffer, sizeof(g_log_buffer),
                        "JSON解析错误 (%s): %s", filename.c_str(), e.what());
                strategy_log(StrategyLogLevel_Error, g_log_buffer);
            } catch (const std::exception& e) {
                snprintf(g_log_buffer, sizeof(g_log_buffer),
                        "读取文件错误 (%s): %s", filename.c_str(), e.what());
                strategy_log(StrategyLogLevel_Error, g_log_buffer);
            }
        }
    }

    closedir(dir);

    // 记录总结信息
    snprintf(g_log_buffer, sizeof(g_log_buffer),
            "板块监控初始化完成: 总共添加了 %d 个板块分组, 包含 %d 只股票",
            total_groups, total_stocks);
    strategy_log(StrategyLogLevel_Info, g_log_buffer);
}

// 成交回报回调处理函数
void OnTradeReport(const Trade *trade, void *user_data) {

    // ostringstream osstream;
    // osstream << "OnTradeReport: " << endl
    //         << "\tstrategy_id: " << trade->strategy_id << endl
    //         << "\trun_id: " << trade->run_id << endl
    //         << "\torder_id: " << trade->order_id << endl
    //         << "\tcl_order_id: " << trade->cl_order_id << endl
    //         << "\tsymbol: " << trade->symbol << endl
    //         << "\taccount_id: " << trade->account_id << endl
    //         << "\taccount_type: " << trade->account_type << endl
    //         << "\tdate: " << trade->date << endl
    //         << "\ttrade_seqno: " << trade->trade_seqno << endl
    //         << "\tside: " << trade->side << endl
    //         << "\torder_type: " << trade->order_type << endl
    //         << "\texec_type: " << trade->exec_type << endl
    //         << "\texec_id: " << trade->exec_id << endl
    //         << "\tvolume: " << trade->volume << endl
    //         << "\tprice: " << trade->price << endl
    //         << "\tturnover: " << trade->turnover << endl
    //         << "\ttransact_time: " << trade->transact_time;
    // strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

// 委托应答回调处理函数
void OnOrderRsp(const OrderRsp *order_rsp, int count, void *user_data) {
    // 输出委托应答到strategy目录的日志中
    ostringstream osstream;
    for (int i = 0; i < count; ++i) {
        osstream.str("");
        osstream << "OrderRsp: " << endl
                << "\torder_id: " << order_rsp[i].order_id << endl
                << "\tcl_order_id: " << order_rsp[i].cl_order_id << endl
                << "\terr_code: " << order_rsp[i].err_code << endl
                << "\terr_msg: " << order_rsp[i].err_msg;
        strategy_log(StrategyLogLevel_Info, osstream.str().data());
    }
}

// 委托状态回调处理函数
void OnOrderStatus(const Order *res, void *user_data) {

    if (res->side != 1) {
        return;
    }
    
    // 查找对应的订阅索引
    if (res->order_status == 2) {
        ostringstream cancel_log0;
        cancel_log0 << "测试单log " << res->symbol
                << ", 订单号: " << res->order_id << " price:" << res->price;
        int idx = find_any_subscription_index(res->symbol);
        if (idx >= 0) {
            int64_t limit_down_price = g_limitup_subscriptions[idx].limit_down_price;
            ostringstream cancel_log;
            cancel_log << "测试单log " << res->symbol
                    << ", 订单号: " << res->order_id << " price:" << res->price;

            strategy_log(StrategyLogLevel_Info, cancel_log.str().data());
            if (res->price == limit_down_price + 200) {
                // 执行撤单
                CancelDetail *cancelList = NULL;
                int cancelCount = 0;
                int ret = td_cancel_order(kStockAccount, AccountType_Stock, res->order_id,
                                        &cancelList, &cancelCount);
                if (ret != 0) {
                    ostringstream cancel_log1;
                    cancel_log1 << "测试单撤单失败: " << res->symbol
                            << ", 订单号: " << res->order_id
                            << ", 错误码: " << ret
                            << ", 错误信息: " << hft_strerror(ret);
                    strategy_log(StrategyLogLevel_Info, cancel_log1.str().data());
                }
                ostringstream cancel_log2;
                cancel_log2 << "测试单log " << res->symbol
                        << ", 订单号: " << res->order_id << " price:" << res->price;
                strategy_log(StrategyLogLevel_Info, cancel_log2.str().data());

            }
        }
    }
    // 检查是否是测试单（volume=100 且 price=跌停价+300）


    
    // 处理正常订单的完成状态
    if (res->order_status == 4) {
        UnsubscribeLimitUpDetection(res->symbol);
        // todo log 可能没有触发

    }

    // 输出委托状态到strategy目录的日志中
    ostringstream osstream;
    osstream << "OnOrderStatus: " << endl
            << "\tsymbol: " << res->symbol << endl
            << "\torder_id: " << res->order_id << endl  // 添加订单ID便于跟踪
            << "\tcreate_time: " << res->create_time << endl
            << "\tupdate_time: " << res->update_time << endl
            << "\tprice: " << (res->price / 10000.0) << "元" << endl  // 添加价格便于识别测试单
            << "\torder_status: " << res->order_status << endl
            << "\tvolume: " << res->volume << "股" << endl  // 添加数量便于识别测试单
            << "\tfilled_volume: " << res->filled_volume << endl
            << "\tfilled_turnover: " << res->filled_turnover;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

// 行情服务状态回调
void OnMdStatusChange(int conn_status, void *user_data) {
    ostringstream osstream;
    osstream << "OnMdStatusChange: " << conn_status;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

void do_block_monitor() {
    for (int i = 0; i < g_sector_group_count; ++i) {
        SectorGroup& group = g_sector_groups[i];
        if (!group.is_active) continue;

        // 如果分组内已有下单，取消订阅其他股票
        if (strlen(group.ordered_symbol) > 0) {
            for (int j = 0; j < group.code_count; ++j) {
                // 跳过已下单的股票
                if (is_symbol_equal(group.codes[j], group.ordered_symbol)) {
                    continue;
                }

                // 取消订阅其他股票
                UnsubscribeLimitUpDetection(group.codes[j]);
            }
            memset(group.ordered_symbol, 0, sizeof(group.ordered_symbol));
            continue; // 已有下单的分组不再检查新订阅
        }

        // 检查分组内的股票是否有涨幅超标的
        for (int j = 0; j < group.code_count; ++j) {
            int bitmap_index = symbol_to_int(group.codes[j]);

            // 检查位图：如果涨幅超过6%且股票类型与分组类型匹配
            if (bitmap_index >= 0 && is_gain_stock_set(bitmap_index)) {
                LimitType stock_limit_type = get_stock_limit_type(group.codes[j]);

                // 只有当股票的涨跌停类型与分组类型匹配时才订阅
                if (stock_limit_type == group.limit_type) {
                    // 检查是否已经在基本监控中
                    if (find_any_subscription_index(group.codes[j]) < 0) {
                        // 添加到基本监控
                        if (g_subscription_count < MAX_SUBSCRIPTIONS) {
                            // 创建新订阅，使用0阈值启用FCode模式
                            SubscribeLimitUpDetection(group.codes[j], 0);

                            snprintf(g_log_buffer, sizeof(g_log_buffer),
                                    "板块监控自动订阅: %s (分组: %s, 类型: %s)",
                                    group.codes[j], group.group_name,
                                    (stock_limit_type == LIMIT_10CM ? "10CM" : "20CM"));
                            strategy_log(StrategyLogLevel_Info, g_log_buffer);
                        }
                    }
                }
            }
        }
    }

    // 清空位图，准备下一轮
    clear_all_gain_stocks();
}

// 定时回调处理函数
void OnStrategyTimer(int interval, void *user_data) {
    ostringstream osstream;

    if (interval == 1000) {
        // 每秒执行板块监控逻辑
        do_block_monitor();
    }

    if (interval == 5000) {
        // 构建 JSON 数组用于自定义指标
        json indexes;

        // 遍历紧凑数组中的所有活跃股票
        for (int i = 0; i < g_active_watching_symbols.count; ++i) {
            int idx = g_active_watching_symbols.indices[i];
            if (idx >= 0 && idx < g_subscription_count) {
                const LimitUpSubscription &sub = g_limitup_subscriptions[idx];
                const SZRealTimeData &data = g_sz_realtime_data[idx];

                // 为每只股票创建一个指标对象
                char key[32], name[32];
                snprintf(key, sizeof(key), "code%d", i + 1);
                snprintf(name, sizeof(name), "code%d", i + 1);

                // 获取股票名称
                std::string stock_name = get_stock_name(std::string(sub.symbol));

                // 构建股票信息字符串，显示名称而不是代码
                char value[256];
                snprintf(value, sizeof(value), "%s(%lld,%s,%lld)",
                        stock_name.c_str(),
                        (long long)sub.threshold_volume,
                        sub.is_ordered ? "yes" : "no",
                        (long long)data.order_time);

                // 添加到 JSON 数组
                json item;
                item["key"] = key;
                item["name"] = name;
                item["type"] = "string";
                item["value"] = value;
                indexes.push_back(item);
            }
        }

        // 添加活跃股票数量指标
        json count_item;
        count_item["key"] = "active_count";
        count_item["name"] = "active_count";
        count_item["type"] = "int";
        count_item["value"] = g_active_watching_symbols.count;
        indexes.push_back(count_item);

        // 上报指标
        strategy_report_indexes(indexes.dump().c_str());
    }
}

void OnDayTaskCallback(int time, void *user_data) {
    ostringstream osstream;
    std::cout << "OnDayTaskCallback: " << time << std::endl;

    orderCount = 0; //置0，让当天又可以下单交易
    if (g_orderids.size() > 0)
        g_orderids.clear();

    osstream << "OnDayTaskCallback: " << time;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());
}

void OnExit(int reason, void *user_data) {
    // 退出回调做一些资源回收等最终操作
    ostringstream osstream;
    osstream << "OnExit, reason: " << strategy_exit_reason(reason);
    const string &message = osstream.str();
    cout << message << endl;
    strategy_log(StrategyLogLevel_Info, message.data());
    std::cout << "OnExit, reason: " << strategy_exit_reason(reason);
}

#ifdef _WIN32
bool CtrlHandler(DWORD fdwctrltype) {
    switch (fdwctrltype) {
        // handle the ctrl-c signal.
        case CTRL_C_EVENT:
            strategy_exit();
            return (true);

        default:
            return false;
    }
}
#endif

// 委托队列数据回调处理函数
void OnOrderQueue(OrderQueueData *res, void *user_data) {
    // 高性能查找订阅
    int sub_index = find_subscription_index(res->symbol);
    if (sub_index >= 0 && g_limitup_subscriptions[sub_index].is_active) {
        // 找到了订阅的股票，打印委托队列信息
        ostringstream log;
        log << "=== 委托队列数据 ===" << endl
                << "证券代码: " << res->symbol << endl;

        // 遍历两档委托队列数据
        for (int i = 0; i < 2; i++) {
            const OrderQueueItemData &item = res->item[i];
            log << "档位 " << (i + 1) << ":" << endl
                    << "  委托时间: " << item.time << endl
                    << "  买卖方向: " << (item.side == 'B' ? "买入" : "卖出") << endl
                    << "  委托价格: " << (item.price / 10000.0) << " 元" << endl
                    << "  委托数量: " << item.order_num << endl
                    << "  委托明细数量: " << item.item_num << endl;

            // 打印委托明细（最多显示前10个）
            log << "  委托明细: ";
            int display_count = item.item_num;
            for (int j = 0; j < display_count; j++) {
                log << item.volume[j];
                if (j < display_count - 1) {
                    log << ", ";
                }
            }
            // if (item.item_num > 10) {
            //     log << ", ... (还有" << (item.item_num - 10) << "条)";
            // }
            log << endl;
        }

        strategy_log(StrategyLogLevel_Info, log.str().data());
    }
}

#ifndef STRATEGY_TEST_MODE
int main(int argc, const char *argv[]) {
    // 处理命令行CTRL-C退出信号
#ifdef _WIN32
    if (!SetConsoleCtrlHandler((PHANDLER_ROUTINE) CtrlHandler, TRUE)) {
        cout << "ERROR: Could not set console control handler" << endl;
        strategy_log(StrategyLogLevel_Error,
                     "ERROR: Could not set console control handler");
        return 1;
    }
#endif
    // 初始化策略
    ostringstream osstream;
    int rc = strategy_init();
    int mode = 0;

    if (0 != rc) {
        osstream << "strategy_init failed: " << rc
                << ", msg: " << hft_strerror_utf8(rc);
        cout << "strategy_init failed: " << rc
                << ", msg: " << hft_strerror(rc) << endl;
        strategy_log(StrategyLogLevel_Error, osstream.str().data());
        goto OnProgramExit;
    }

    // 初始化紧凑数组和位图
    g_active_watching_symbols.clear();
    clear_all_gain_stocks();

    osstream << "strategy_init success" << endl;
    osstream << "LimitUpSubscription size: " << sizeof(LimitUpSubscription) << endl;
    osstream << "SzRealTimeData size: " << sizeof(SZRealTimeData) << endl;
    strategy_log(StrategyLogLevel_Info, osstream.str().data());

    // 读取JSON文件并添加板块监控
    read_and_add_sector_groups_from_json();

    // 读取股票代码名称映射
    read_code_name();

    // 初始化封单检测（支持SH/SZ双市场）- 使用char[12]格式
    char symbol1[12], symbol2[12], symbol3[12], symbol4[12];

    // 设置交易相关回调事件处理函数
    td_set_trade_report_callback(OnTradeReport, NULL);
    td_set_order_rsp_callback(OnOrderRsp, NULL);
    td_set_order_status_callback(OnOrderStatus, NULL);

    // 设置实时行情回调事件处理函数
    md_set_status_change_callback(OnMdStatusChange, NULL);

    // 设置行情回调函数
    md_set_security_tick_callback(OnSecurityTick, NULL); //证券tick行情回调
    md_set_index_tick_callback(OnIndexTickCallback, NULL);
    md_set_security_kdata_callback(OnSecurityKdata, NULL); //证券分钟K线回调
    md_set_tickbytick_callback(OnTickbytickData, NULL);
    // md_set_orderqueue_callback(OnOrderQueue, NULL);  // 委托队列数据回调

    md_set_date_update_callback(OnDateUpdate, NULL); //日期变化回调

    // 设置定时任务回调处理函数
    strategy_set_timer_callback(OnStrategyTimer, NULL);

    // 设置策略退出回调处理函数
    strategy_set_exit_callback(OnExit, NULL);

    // 设置一个5s定时任务
    strategy_set_timer(5000);
    strategy_set_timer(1000);


    strategy_set_day_schedule_task_callback(OnDayTaskCallback, NULL);
    strategy_set_day_schedule_task(85000);

    // 订阅一个Tick，多个K线行情
    // md_subscribe("SH.601211.tick,SH.601211.bar,SH.000001.index");

    strategy_set_params_setting_callback(OnStrategyParamsSetting, NULL);


    char myparam[512];
    snprintf(myparam, sizeof(myparam),
             "["
             "{\"key\":\"Code\", \"name\":\"Code\", \"type\":\"string\", \"value\":\"SH.600002\"},"
             "{\"key\":\"Log\", \"name\":\"Log\", \"type\":\"string\", \"value\":\"0\"},"
             "{\"key\":\"FCode\", \"name\":\"FCode\", \"type\":\"string\", \"value\":\"SH.600002\"},"

             "{\"key\":\"DCode\", \"name\":\"DCode\", \"type\":\"string\", \"value\":\"SH.\"}"

             "]");
    strategy_report_params(myparam);

    init_order_req();


    // 运行策略
    // mode 0 - 默认模式, 事件触发
    // 1 - spin模式，通过死循环检测事件队列中是否有新的事件到达。
    rc = strategy_run(1);
    if (rc != 0) {
        osstream.str("");
        osstream << "strategy_run failed: " << rc
                << ", msg: " << hft_strerror_utf8(rc);
        cout << "strategy_run failed: " << rc
                << ", msg: " << hft_strerror(rc) << endl;
        strategy_log(StrategyLogLevel_Error, osstream.str().data());
    } else {
        cout << "strategy_run success" << endl;
        strategy_log(StrategyLogLevel_Info, "strategy_run success");
    }

    // 主线程等待策略线程退出
    while (strategy_get_exec_status() != StrategyExecStatus_Term) {
        // sleep 500ms
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

OnProgramExit:
    if (strategy_get_exec_status() != StrategyExecStatus_Term) {
        rc = strategy_exit();
        if (rc != 0) {
            osstream.str("");
            osstream << "strategy_exit failed: " << rc
                    << ", msg: " << hft_strerror_utf8(rc);
            cout << "strategy_exit failed: " << rc
                    << ", msg: " << hft_strerror(rc) << endl;
            strategy_log(StrategyLogLevel_Info, osstream.str().data());
        } else {
            cout << "strategy_exit success" << endl;
            strategy_log(StrategyLogLevel_Info, "strategy_exit success");
        }
    }
    return 0;
}
#endif // STRATEGY_TEST_MODE
