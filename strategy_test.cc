#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <sstream>
#include <chrono>
#include <thread>
#include <cstring>
#include <cassert>

// 测试结果结构体 - 必须在mock函数之前定义
struct TestResult {
    int orderReceived;
    int cancelReceived;
    int tickCount;
    std::string orderId;
};

// 全局测试结果
TestResult g_testResult;

// 定义测试模式宏，防止 simple_strategy.cc 中的 main 函数被编译
#define STRATEGY_TEST_MODE

// 在包含任何头文件之前定义宏来重定向DLL导入符号
#define strategy_log mock_strategy_log
#define td_order mock_td_order
#define td_cancel_order mock_td_cancel_order
#define hft_strerror mock_hft_strerror
#define hft_strerror_utf8 mock_hft_strerror_utf8
#define get_security_ticks mock_get_security_ticks
#define get_security_kdata mock_get_security_kdata
#define strategy_init mock_strategy_init
#define strategy_exit mock_strategy_exit
#define strategy_exit_reason mock_strategy_exit_reason
#define strategy_report_indexes mock_strategy_report_indexes

#include "md_api.h"
#include "strategy_api.h"
#include "trade_api.h"
#include "md_def.h"
#include "trade_def.h"
// #include "strategy_test.h"

// 定义mock函数
extern "C" {
    void mock_strategy_log(StrategyLogLevel level, const char* message, bool is_gbk) {
        const char* level_str = "UNKNOWN";
        switch(level) {
            case StrategyLogLevel_Debug: level_str = "DEBUG"; break;
            case StrategyLogLevel_Info: level_str = "INFO"; break;
            case StrategyLogLevel_Warn: level_str = "WARN"; break;
            case StrategyLogLevel_Error: level_str = "ERROR"; break;
        }
        std::cout << "[MOCK LOG " << level_str << "] " << message << std::endl;
    }

    int mock_strategy_init(const char* config_dir, const char* log_dir) {
        std::cout << "[MOCK] strategy_init called" << std::endl;
        return 0;
    }

    int mock_strategy_exit() {
        return 0;
    }

    const char* mock_strategy_exit_reason(int reason) {
        return "Mock exit reason";
    }

    int mock_strategy_report_indexes(const char* indexes_json) {
        std::cout << "[MOCK] strategy_report_indexes: " << indexes_json << std::endl;
        return 0;
    }

    int mock_td_order(const char* account_id, AccountType account_type, OrderReq* orders, int reqnum, int async) {

        // 模拟成功下单并直接更新测试结果
        for (int i = 0; i < reqnum; i++) {
            // 过滤测试单：10cm股票的测试单价格90200/90300，20cm股票的测试单价格80200
            if (orders[i].price == 90200 || orders[i].price == 80200 || orders[i].price == 90300) continue;
            // 直接更新测试结果
            g_testResult.orderReceived += 1;
            g_testResult.orderId = orders[i].order_id;
            std::cout << "DEBUG: SendOrder called #" << g_testResult.orderReceived
                      << " for symbol: " << orders[i].symbol
                      << ", price: " << orders[i].price << std::endl;
        }
        return 0;
    }

    int mock_td_cancel_order(const char* account_id, AccountType account_type, const char* order_ids,
                            CancelDetail** cancel_list, int* count) {
        // 直接更新测试结果
        g_testResult.cancelReceived += 1;

        // 模拟撤单成功
        if (cancel_list && count) {
            *count = 0;
            *cancel_list = nullptr;
        }

        return 0;
    }

    const char* mock_hft_strerror(int err) {
        static char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), "[MOCK] Error code: %d", err);
        return error_msg;
    }

    const char* mock_hft_strerror_utf8(int err) {
        return mock_hft_strerror(err);
    }

    int mock_get_security_ticks(const char* symbol_list, const char* begin_time, const char* end_time,
                               SecurityTickData** std, int* count) {
        std::cout << "[MOCK] get_security_ticks called - symbols: " << symbol_list << std::endl;

        if (std && count) {
            *count = 0;
            *std = nullptr;
        }
        return 0;
    }

    int mock_get_security_kdata(const char* symbol_list, const char* begin_date, const char* end_date,
                               const char* frequency, const char* fq, SecurityKdata** skd, int* count) {
        std::cout << "[MOCK] get_security_kdata called - symbols: " << symbol_list << std::endl;

        if (skd && count) {
            *count = 0;
            *skd = nullptr;
        }
        return 0;
    }
}

// 现在包含simple_strategy.cc，它会使用上面定义的mock函数
#include "simple_strategy.cc"

// 添加板块监控测试的辅助函数
bool LoadSectorConfig(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Cannot open sector config file: " << config_file << std::endl;
        return false;
    }

    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    file.close();

    try {
        json config = json::parse(content);

        // 只支持新格式：直接的板块对象
        if (!config.is_object()) {
            std::cerr << "Invalid JSON format: expected object" << std::endl;
            return false;
        }

        for (auto it = config.begin(); it != config.end(); ++it) {
            const std::string& sector = it.key();
            const json& sector_data = it.value();

            // 检查是否包含codes数组和reason字段
            if (!sector_data.contains("codes") || !sector_data["codes"].is_array()) {
                std::cerr << "Sector " << sector << " missing valid codes array" << std::endl;
                continue;
            }

            const json& codes = sector_data["codes"];
            std::string reason = sector_data.contains("reason") ? sector_data["reason"].get<std::string>() : "未指定原因";

            // 添加板块分组
            if (AddSectorGroup(sector.c_str(), reason.c_str(), codes)) {
                std::cout << "Added sector group: " << sector
                          << " (reason: " << reason << ") with " << codes.size() << " stocks" << std::endl;
            }
        }
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error parsing sector config: " << e.what() << std::endl;
        return false;
    }
}


#include <map>

// 测试用例数据结构
struct TestCase {
    std::string description;
    std::string dataFilePath;
    int expectOrder;
    int expectCancel;
};



// 模拟的行情数据
std::vector<std::string> g_marketDataLines;
int g_currentLine = 0;

// 触发订单状态回调的函数
void triggerOrderStatusCallback(const std::map<std::string, int64_t>& fields,
                               const std::map<std::string, std::string>& string_fields,
                               const std::string& symbol) {
    Order order;
    memset(&order, 0, sizeof(Order));

    // 填充Order结构体的基本信息
    strcpy(order.strategy_id, "test_strategy");
    strcpy(order.run_id, "test_run");
    strcpy(order.symbol, symbol.c_str());
    strcpy(order.account_id, "test_account");

    // 从数据中获取订单信息
    if (string_fields.count("order_id")) {
        strcpy(order.order_id, string_fields.at("order_id").c_str());
        strcpy(order.cl_order_id, string_fields.at("order_id").c_str());
    } else {
        strcpy(order.order_id, "TEST_ORDER_001");
        strcpy(order.cl_order_id, "TEST_ORDER_001");
    }

    // 设置订单状态和其他字段
    order.order_status = fields.count("status") ? (int16_t)fields.at("status") : OrderStatus_New;
    order.price = fields.count("price") ? fields.at("price") : 110000;
    order.volume = fields.count("volume") ? (int32_t)fields.at("volume") : 1000;
    order.filled_volume = fields.count("filled_volume") ? (int32_t)fields.at("filled_volume") : 0;
    order.filled_turnover = fields.count("filled_turnover") ? fields.at("filled_turnover") : 0;
    order.side = 1; // 买入
    order.order_type = 1; // 限价单
    order.account_type = 1; // 普通账户
    order.date = ********; // 测试日期

    // 设置时间戳（微秒格式）
    order.create_time = fields.count("create_time") ? fields.at("create_time") : ***********;
    order.update_time = fields.count("update_time") ? fields.at("update_time") : ***********;
    order.marketdata_time = fields.count("marketdata_time") ? fields.at("marketdata_time") : ***********;

    // 调用OnOrderStatus回调
    OnOrderStatus(&order, nullptr);

}

// 解析行情数据行
bool parseMarketDataLine(const std::string& line, TickByTickData* tbt, SecurityTickData* tick) {
    // 示例格式: [100001]|SH.600002|TICK|V:100000|T:********|N:1000001|M:10000|TAV:150000|BP1:10000|BV1:100000

    // 或者: [377639]|SH.605255|BW|V:29800|T:********|N:2395706
    
    std::istringstream iss(line);
    std::string timestamp, symbol, type;
    std::map<std::string, int64_t> fields;
    
    char buffer[1024];
    strncpy(buffer, line.c_str(), sizeof(buffer));
    buffer[sizeof(buffer) - 1] = '\0';
    
    char* token = strtok(buffer, "|");
    if (!token) return false;
    timestamp = token;
    
    token = strtok(nullptr, "|");
    if (!token) return false;
    symbol = token;
    
    token = strtok(nullptr, "|");
    if (!token) return false;
    type = token;
    
    // 解析所有剩余字段
    std::map<std::string, std::string> string_fields; // 用于存储字符串字段
    while ((token = strtok(nullptr, "|")) != nullptr) {
        std::string field = token;
        size_t pos = field.find(':');
        if (pos != std::string::npos) {
            std::string key = field.substr(0, pos);
            std::string value = field.substr(pos + 1);
            string_fields[key] = value; // 保存原始字符串值
            int64_t numValue = 0;
            sscanf(value.c_str(), "%lld", &numValue);
            fields[key] = numValue;
        }
    }
    
    // 提取时间戳
    int64_t timestampValue = 0;
    sscanf(timestamp.c_str() + 1, "%lld", &timestampValue);
    
    // 根据类型填充不同的数据结构
    if (type == "TICK") {
        // 填充Tick数据 - 从字段映射中获取值
        memset(tick, 0, sizeof(SecurityTickData));
        strcpy(tick->symbol, symbol.c_str());
        
        // 必须存在的字段
        tick->time = fields["T"];
        tick->volume = fields["V"];
        
        // 从字段映射中获取值，如果不存在则使用默认值
        tick->match = fields.count("M") ? fields["M"] : 108000;

        
        // 设置基本字段
        tick->open = tick->match; // 使用最新价作为开盘价

        // 根据股票类型设置不同的涨跌停价
        if (is_10cm_limit_stock(tick->symbol)) {
            tick->high_limited = 110000; // 10cm股票涨停价
            tick->low_limited = 90000;   // 10cm股票跌停价
        } else {
            tick->high_limited = 120000; // 20cm股票涨停价
            tick->low_limited = 80000;   // 20cm股票跌停价
        }

        tick->pre_close = fields.count("PC") ? fields["PC"] : 100000;    // 前收盘价，用于7%涨幅计算

        // 设置买卖盘数据
        tick->bid_vol[0] = fields.count("BV1") ? fields["BV1"] : 100000;


        // 设置总委买卖量
        tick->total_ask_vol = fields.count("TAV") ? fields["TAV"] : fields["V"];
        
        // 设置成交相关数据 - 如果没有提供，则根据成交量和价格计算
        tick->turnover = fields.count("TO") ? fields["TO"] : 100000000;
        
        return true;
    }
    else if (type == "BT") {
        // 逐笔成交数据
        memset(tbt, 0, sizeof(TickByTickData));
        strcpy(tbt->symbol, symbol.c_str());
        tbt->data_time = fields["T"];
        tbt->type = '1'; // 成交

        // 填充成交数据
        tbt->data.trade.price = fields.count("P") ? fields["P"] : 110000; // 默认涨停价
        tbt->data.trade.qty = fields["V"];
        tbt->data.trade.trade_flag = string_fields.count("TF") ? string_fields["TF"][0] : 'B'; // 默认买入成交
        tbt->data.trade.bid_no = fields.count("BN") ? fields["BN"] : 1001; // 买方订单号
        tbt->data.trade.ask_no = fields.count("AN") ? fields["AN"] : 2001; // 卖方订单号

        return true;
    }
    else if (type == "BW" || type == "CW" || type == "SW") {
        // 填充逐笔数据
        memset(tbt, 0, sizeof(TickByTickData));
        strcpy(tbt->symbol, symbol.c_str());
        tbt->data_time = fields["T"];
        
        if (type == "BW") {
            // 买入委托
            tbt->type = '0'; // 委托
            tbt->data.entrust.side = '1'; // 买入
            tbt->data.entrust.ord_type = 'A'; // 增加
            tbt->data.entrust.price = 110000; // 涨停价
            tbt->data.entrust.qty = fields["V"];
            tbt->data.entrust.price = fields.count("P") ? fields["P"] : 110000; // 从数据中读取价格，默认涨停价
        }
        else if (type == "CW") {
            // 撤单委托
            tbt->type = '0'; // 委托
            tbt->data.entrust.side = '1'; // 买入
            tbt->data.entrust.ord_type = 'D'; // 删除
            tbt->data.entrust.price = 110000; // 涨停价
            tbt->data.entrust.qty = fields["V"];
        }
        else if (type == "SW") {
            // 卖出委托
            tbt->type = '0'; // 委托
            tbt->data.entrust.side = '2'; // 卖出
            tbt->data.entrust.ord_type = 'A'; // 增加
            tbt->data.entrust.price = 110000; // 涨停价
            tbt->data.entrust.qty = fields["V"];
        }

        return true;
    }
    else if (type == "ORDER_STATUS") {
        // 订单状态变化数据
        // 格式: [timestamp]|symbol|ORDER_STATUS|order_id:xxx|status:2|price:110000|volume:1000|filled_volume:0
        triggerOrderStatusCallback(fields, string_fields, symbol);
        return true;
    }

    return false;
}



// 从文件加载测试数据
bool loadTestData(const std::string& filePath) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "Cannot open file: " << filePath << std::endl;
        return false;
    }
    
    g_marketDataLines.clear();
    std::string line;
    while (std::getline(file, line)) {
        if (!line.empty()) {
            g_marketDataLines.push_back(line);
        }
    }
    
    file.close();
    g_currentLine = 0;
    return !g_marketDataLines.empty();
}

// 运行单个测试用例
bool runTestCase(const TestCase& testCase) {
    std::cout << "Starting test: " << testCase.description << std::endl;

    // 重置测试结果
    g_testResult.orderReceived = 0;
    g_testResult.cancelReceived = 0;
    g_testResult.tickCount = 0;
    g_testResult.orderId = "";

    // 重置板块分组状态（确保测试用例之间的独立性）
    for (int i = 0; i < g_sector_group_count; ++i) {
        memset(g_sector_groups[i].ordered_symbol, 0, sizeof(g_sector_groups[i].ordered_symbol));
    }

    // 重置订阅状态
    g_subscription_count = 0;
    memset(g_limitup_subscriptions, 0, sizeof(g_limitup_subscriptions));
    memset(g_sz_realtime_data, 0, sizeof(g_sz_realtime_data));
    // 加载一个公用的板块监控（使用新格式）
    read_and_add_sector_groups_from_json();

    // 为300ms窗口测试订阅特殊股票（阈值0，统一使用0作为判断条件）
    if (testCase.description.find("300ms") != std::string::npos ||
        testCase.description.find("滑动窗口") != std::string::npos) {
        SubscribeLimitUpDetection("SH.600002", 0);  // 300ms窗口模式
        SubscribeLimitUpDetection("SZ.000001", 0);  // 300ms窗口模式
    }

    // 为慢排版模式测试订阅特殊股票（设置具体阈值）
    if (testCase.description.find("慢排版") != std::string::npos) {
        SubscribeLimitUpDetection("SH.600002", 500000000000);  // 5000万阈值
        SubscribeLimitUpDetection("SZ.000001", 500000000000);  // 5000万阈值
    }

    // 加载测试数据
    if (!loadTestData(testCase.dataFilePath)) {
        std::cerr << "Failed to load test data" << std::endl;
        return false;
    }
    
    // 设置回调
    
    // 处理每一行数据
    for (const auto& line : g_marketDataLines) {
        TickByTickData tbt;
        SecurityTickData tick;
        
        if (parseMarketDataLine(line, &tbt, &tick)) {
            // 根据类型调用不同的回调
            if (line.find("TICK") != std::string::npos) {
                OnSecurityTick(&tick, nullptr);
                do_block_monitor();

            } else {
                OnTickbytickData(&tbt, nullptr);
            }
            
            g_testResult.tickCount++;
        }
    }
    
    // 验证测试结果
    bool success = true;
    if (testCase.expectOrder != g_testResult.orderReceived) {
        std::cerr << testCase.description << " Test failed: Expected " << testCase.expectOrder
                  << " orders but got " << g_testResult.orderReceived << std::endl;
        success = false;
    }

    if (testCase.expectCancel != g_testResult.cancelReceived) {
        std::cerr<< testCase.description << " Test failed: Expected " << testCase.expectCancel
                  << " cancels but got " << g_testResult.cancelReceived << std::endl;
        success = false;
    }
    
    std::cout << "Test " << (success ? "passed" : "failed") << ": " << testCase.description << std::endl;
    return success;
}

// 创建测试数据文件
bool createTestDataFile(const std::string& filePath, const std::vector<std::string>& lines) {
    std::ofstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filePath << std::endl;
        return false;
    }
    
    for (const auto& line : lines) {
        file << line << std::endl;
    }
    
    file.close();
    return true;
}




// 主函数
int main() {

    
    // 初始化订单请求
    init_order_req();

    // 300ms窗口测试的订阅现在在runTest函数中处理

    // Define test cases
    std::vector<TestCase> testCases = {
        // {"Scenario 1: order and fast cancel", "test_fast_cancel.txt", 1, 1},  // 期望下单和撤单
        {"Scenario 1: 开板", "test/test_kaiban_order.txt", 0, 0},  // 期望下单和撤单
        {"Scenario 1: 931小单上板不打", "test/test_31.txt", 0, 0},  // 期望下单和撤单
        {"Scenario 1: 932小单上板打", "test/test_32.txt", 1, 1},  // 期望下单和撤单
        {"Scenario 1: 932小回封不打", "test/test_32_small_re.txt", 1, 1},  // 期望下单和撤单
        {"Scenario 1: 932大回封打", "test/test_32_big_re.txt", 2, 1},  // 期望下单和撤单
        // {"Scenario 10: 慢排版模式时间戳测试", "test_slow_mode_timestamp.txt", 0, 0},  // 期望0个下单（主要测试slow_mode管理）
        {"Scenario: 订单状态4不再下单", "test/test_order_status4.txt", 0, 0},  // 新增：订单状态影响下单逻辑的联动测试
        {"Scenario: 订单状态7再下单", "test/test_order_status7.txt", 1, 0},  // 新增：订单状态影响下单逻辑的联动测试

        {"Scenario 3: 板块监控测试", "test/test_sector_monitor.txt", 1, 0, },  // 期望只有第一只股票下单
        {"Scenario 4: 涨跌停幅度分组测试", "test/test_limit_group_monitor.txt", 2, 0, },  // 期望10cm和20cm分组各下单一只
        {"Scenario 4: 上海BW累计异常case", "test/test_31_shbwcase.txt", 0, 0, },  // 期望10cm和20cm分组各下单一只


        // {"Scenario 5: 300ms窗口提前下单测试", "test_early_order_300ms.txt", 2, 0, },  // 期望沪深两市各下单一只
        // {"Scenario 6: 不应该下单的边界测试", "test_no_order_scenarios.txt", 0, 0, },  // 期望0个下单
        // {"Scenario 9: 打板模式动态阈值测试", "test_dynamic_threshold.txt", 0, 0, },  // 期望0个下单（主要测试阈值调整）
        // {"Scenario 10: Cancel After 4 Ticks", "test_case6.txt", true, true, }
    };
    
    // 运行测试用例
    int passCount = 0;
    for (const auto& testCase : testCases) {
        if (runTestCase(testCase)) {
            passCount++;
        }
    }
    
    std::cout << "Test completed: " << passCount << "/" << testCases.size() << " passed" << std::endl;
    
    // 退出策略
    strategy_exit();
    
    return (passCount == testCases.size()) ? 0 : 1;
} 