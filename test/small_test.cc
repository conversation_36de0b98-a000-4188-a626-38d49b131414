#define STRATEGY_TEST_MODE

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <sstream>
#include <chrono>
#include <thread>
#include <cstring>
#include <cassert>


#include "../simple_strategy.cc"

// 在包含任何头文件之前定义宏来重定向DLL导入符号
#define strategy_log mock_strategy_log
#define td_order mock_td_order
#define td_cancel_order mock_td_cancel_order
#define hft_strerror mock_hft_strerror
#define hft_strerror_utf8 mock_hft_strerror_utf8
#define get_security_ticks mock_get_security_ticks
#define get_security_kdata mock_get_security_kdata
#define strategy_init mock_strategy_init
#define strategy_exit mock_strategy_exit
#define strategy_exit_reason mock_strategy_exit_reason
#define strategy_report_indexes mock_strategy_report_indexes
// 定义mock函数
extern "C" {
    void mock_strategy_log(StrategyLogLevel level, const char* message, bool is_gbk) {

    }

    int mock_strategy_init(const char* config_dir, const char* log_dir) {
        std::cout << "[MOCK] strategy_init called" << std::endl;
        return 0;
    }

    int mock_strategy_exit() {
        return 0;
    }

    const char* mock_strategy_exit_reason(int reason) {
        return "Mock exit reason";
    }

    int mock_strategy_report_indexes(const char* indexes_json) {
        std::cout << "[MOCK] strategy_report_indexes: " << indexes_json << std::endl;
        return 0;
    }

    int mock_td_order(const char* account_id, AccountType account_type, OrderReq* orders, int reqnum, int async) {

        return 0;
    }

    int mock_td_cancel_order(const char* account_id, AccountType account_type, const char* order_ids,
                            CancelDetail** cancel_list, int* count) {

        return 0;
    }

    const char* mock_hft_strerror(int err) {
        static char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), "[MOCK] Error code: %d", err);
        return error_msg;
    }

    const char* mock_hft_strerror_utf8(int err) {
        return mock_hft_strerror(err);
    }

    int mock_get_security_ticks(const char* symbol_list, const char* begin_time, const char* end_time,
                               SecurityTickData** std, int* count) {

        return 0;
    }

    int mock_get_security_kdata(const char* symbol_list, const char* begin_date, const char* end_date,
                               const char* frequency, const char* fq, SecurityKdata** skd, int* count) {

        return 0;
    }
}

using namespace std;



int main() {

    char s[12] = "SH.600610";
    char s1[12] = "SH.600610\01";

    for (int i = 0; i < 12; i++) {
        cout<<s1[i] << "\n";
    }

    cout << is_symbol_equal(s, s1);
    return 0;
}